<?php
/**
 * Auction Penalty System
 * 
 * Main file that coordinates the auction penalty system including:
 * - Winner tracking and 24-hour purchase window
 * - User penalty management (0-3 levels)
 * - Deposit requirements based on penalty levels
 * - Cron job management
 * 
 * @package AuctionPenaltySystem
 * @version 1.0.0
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Main Auction Penalty System Class
 */
class AuctionPenaltySystem {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init();
    }
    
    /**
     * Initialize the system
     */
    private function init() {
        // Include required files
        $this->include_files();
        
        // Initialize hooks
        $this->init_hooks();
        
        // Initialize admin interface
        if (is_admin()) {
            $this->init_admin();
        }
    }
    
    /**
     * Include required files
     */
    private function include_files() {
        $base_path = get_stylesheet_directory() . '/auction-deposit/';
        
        require_once $base_path . 'auction-cron-jobs.php';
        require_once $base_path . 'auction-winner-tracking.php';
        require_once $base_path . 'auction-deposit-requirements.php';
        require_once $base_path . 'auction-admin-interface.php';
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Hook into auction finished event
        add_action('auctions_for_woocommerce_finished', array($this, 'handle_auction_finished'), 10, 1);
        add_action('auctions_for_woocommerce_won', array($this, 'handle_auction_won'), 10, 1);
        
        // Hook into bidding process to check penalties (high priority to override other filters)
        add_filter('auctions_for_woocommerce_user_can_bid', array($this, 'check_user_can_bid'), 999, 3);
        
        // Add penalty info to bid form
        add_action('woocommerce_before_bid_form', array($this, 'display_penalty_warning'), 10);

        // Hook into order completion to mark winners as purchased
        add_action('woocommerce_order_status_completed', array($this, 'handle_order_completed'), 10, 1);
        add_action('woocommerce_order_status_processing', array($this, 'handle_order_completed'), 10, 1);

        // Initialize cron jobs
        add_action('init', array($this, 'setup_cron_jobs'));

        // Handle custom cron endpoints
        add_action('init', array($this, 'handle_cron_requests'));
    }
    
    /**
     * Initialize admin interface
     */
    private function init_admin() {
        new AuctionAdminInterface();
    }
    
    /**
     * Handle auction finished event
     */
    public function handle_auction_finished($product_id) {
        $winner_tracker = new AuctionWinnerTracker();
        $winner_tracker->track_auction_winner($product_id);
    }
    
    /**
     * Handle auction won event
     */
    public function handle_auction_won($product_id) {
        $winner_tracker = new AuctionWinnerTracker();
        $winner_tracker->track_auction_winner($product_id);
    }

    /**
     * Handle order completion - mark auction winners as purchased
     */
    public function handle_order_completed($order_id) {
        $order = wc_get_order($order_id);

        if (!$order) {
            return;
        }

        $customer_id = $order->get_customer_id();

        if (!$customer_id) {
            return;
        }

        // Check each order item to see if it's an auction product
        foreach ($order->get_items() as $item) {
            $product_id = $item->get_product_id();
            $product = wc_get_product($product_id);

            if ($product && $product->is_type('auction')) {
                $winner_tracker = new AuctionWinnerTracker();
                $winner_tracker->mark_as_purchased($product_id, $customer_id);

                error_log("Auction Penalty System: Marked as purchased - Order {$order_id}, Product {$product_id}, Customer {$customer_id}");
            }
        }
    }
    
    /**
     * Check if user can bid based on penalty level
     */
    public function check_user_can_bid($can_bid, $product, $user) {
        if (!$user || !$user->ID) {
            return $can_bid;
        }

        $deposit_requirements = new AuctionDepositRequirements();
        $result = $deposit_requirements->can_user_bid($user->ID, $product ? $product->get_id() : null);

        // Debug log
        if (current_user_can('manage_options') && isset($_GET['debug_bid'])) {
            error_log("Auction Penalty Debug: User {$user->ID}, can_bid original: " . ($can_bid ? 'true' : 'false') . ", penalty result: " . ($result ? 'true' : 'false'));
        }

        return $result;
    }
    
    /**
     * Display penalty warning before bid form
     */
    public function display_penalty_warning() {
        if (!is_user_logged_in()) {
            return;
        }
        
        $user_id = get_current_user_id();
        $penalty_level = get_user_meta($user_id, 'user_penalty', true);
        $penalty_level = intval($penalty_level);
        
        if ($penalty_level > 0) {
            $deposit_requirements = new AuctionDepositRequirements();
            $deposit_requirements->display_penalty_warning($user_id);
        }
    }
    
    /**
     * Setup cron jobs
     */
    public function setup_cron_jobs() {
        $cron_manager = new AuctionCronJobs();
        $cron_manager->setup_cron_schedules();
    }
    
    /**
     * Handle custom cron requests
     */
    public function handle_cron_requests() {
        if (isset($_GET['auction-penalty-cron'])) {
            $cron_type = sanitize_text_field($_GET['auction-penalty-cron']);
            $cron_manager = new AuctionCronJobs();
            
            switch ($cron_type) {
                case 'check-winners':
                    $cron_manager->check_auction_winners();
                    break;
                case 'process-penalties':
                    $cron_manager->process_penalty_updates();
                    break;
            }
            
            // Return success response
            status_header(200);
            echo 'OK';
            exit;
        }
    }
    
    /**
     * Get user penalty level
     */
    public static function get_user_penalty_level($user_id) {
        $penalty = get_user_meta($user_id, 'user_penalty', true);
        return max(0, min(3, intval($penalty)));
    }
    
    /**
     * Set user penalty level
     */
    public static function set_user_penalty_level($user_id, $level) {
        $level = max(0, min(3, intval($level)));
        update_user_meta($user_id, 'user_penalty', $level);
        
        // Log penalty change
        self::log_penalty_change($user_id, $level);
        
        return $level;
    }
    
    /**
     * Increase user penalty level
     */
    public static function increase_user_penalty($user_id) {
        $current_level = self::get_user_penalty_level($user_id);
        $new_level = min(3, $current_level + 1);
        
        return self::set_user_penalty_level($user_id, $new_level);
    }
    
    /**
     * Log penalty changes
     */
    private static function log_penalty_change($user_id, $level) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'auction_penalty_log';
        
        // Create table if it doesn't exist
        self::create_penalty_log_table();
        
        $wpdb->insert(
            $table_name,
            array(
                'user_id' => $user_id,
                'penalty_level' => $level,
                'reason' => 'Failed to purchase won auction within 24 hours',
                'date_created' => current_time('mysql')
            ),
            array('%d', '%d', '%s', '%s')
        );
    }
    
    /**
     * Create penalty log table
     */
    private static function create_penalty_log_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'auction_penalty_log';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            penalty_level tinyint(1) NOT NULL,
            reason text NOT NULL,
            date_created datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Get penalty level description
     */
    public static function get_penalty_description($level) {
        switch (intval($level)) {
            case 0:
                return __('No penalty', 'woocommerce');
            case 1:
                return __('10% deposit required', 'woocommerce');
            case 2:
                return __('40% deposit required', 'woocommerce');
            case 3:
                return __('Bidding blocked', 'woocommerce');
            default:
                return __('Unknown penalty level', 'woocommerce');
        }
    }
}

// Initialize the system
new AuctionPenaltySystem();

/**
 * Activation hook to create database tables
 */
function auction_penalty_system_activate() {
    // Create penalty log table
    AuctionPenaltySystem::create_penalty_log_table();

    // Create winner tracking table
    $winner_tracker = new AuctionWinnerTracker();
    $reflection = new ReflectionClass($winner_tracker);
    $method = $reflection->getMethod('create_tracking_table');
    $method->setAccessible(true);
    $method->invoke($winner_tracker);

    // Set up cron jobs
    if (!wp_next_scheduled('auction_check_winners_cron')) {
        wp_schedule_event(time(), 'every_minute', 'auction_check_winners_cron');
    }

    if (!wp_next_scheduled('auction_process_penalties_cron')) {
        wp_schedule_event(time(), 'hourly', 'auction_process_penalties_cron');
    }
}

/**
 * Deactivation hook to clean up cron jobs
 */
function auction_penalty_system_deactivate() {
    wp_clear_scheduled_hook('auction_check_winners_cron');
    wp_clear_scheduled_hook('auction_process_penalties_cron');
}

// Register activation/deactivation hooks
register_activation_hook(__FILE__, 'auction_penalty_system_activate');
register_deactivation_hook(__FILE__, 'auction_penalty_system_deactivate');

/**
 * Debug function to test the penalty system
 * Add ?test_auction_penalty=1 to any page URL to run tests
 */
function auction_penalty_debug_test() {
    if (isset($_GET['test_auction_penalty']) && current_user_can('manage_options')) {
        echo '<div style="background: #fff; padding: 20px; margin: 20px; border: 1px solid #ccc;">';
        echo '<h3>Auction Penalty System Debug Test</h3>';

        // Test 1: Check if classes are loaded
        echo '<h4>1. Class Loading Test</h4>';
        echo 'AuctionPenaltySystem: ' . (class_exists('AuctionPenaltySystem') ? '✓ Loaded' : '✗ Not loaded') . '<br>';
        echo 'AuctionCronJobs: ' . (class_exists('AuctionCronJobs') ? '✓ Loaded' : '✗ Not loaded') . '<br>';
        echo 'AuctionWinnerTracker: ' . (class_exists('AuctionWinnerTracker') ? '✓ Loaded' : '✗ Not loaded') . '<br>';
        echo 'AuctionDepositRequirements: ' . (class_exists('AuctionDepositRequirements') ? '✓ Loaded' : '✗ Not loaded') . '<br>';
        echo 'AuctionAdminInterface: ' . (class_exists('AuctionAdminInterface') ? '✓ Loaded' : '✗ Not loaded') . '<br>';

        // Test 2: Check database tables
        echo '<h4>2. Database Tables Test</h4>';
        global $wpdb;
        $penalty_table = $wpdb->prefix . 'auction_penalty_log';
        $tracking_table = $wpdb->prefix . 'auction_winner_tracking';

        $penalty_exists = $wpdb->get_var("SHOW TABLES LIKE '$penalty_table'") == $penalty_table;
        $tracking_exists = $wpdb->get_var("SHOW TABLES LIKE '$tracking_table'") == $tracking_table;

        echo "Penalty log table ($penalty_table): " . ($penalty_exists ? '✓ Exists' : '✗ Missing') . '<br>';
        echo "Winner tracking table ($tracking_table): " . ($tracking_exists ? '✓ Exists' : '✗ Missing') . '<br>';

        // Test 3: Check cron jobs
        echo '<h4>3. Cron Jobs Test</h4>';
        $check_winners_scheduled = wp_next_scheduled('auction_check_winners_cron');
        $process_penalties_scheduled = wp_next_scheduled('auction_process_penalties_cron');

        echo 'Check winners cron: ' . ($check_winners_scheduled ? '✓ Scheduled for ' . date('Y-m-d H:i:s', $check_winners_scheduled) : '✗ Not scheduled') . '<br>';
        echo 'Process penalties cron: ' . ($process_penalties_scheduled ? '✓ Scheduled for ' . date('Y-m-d H:i:s', $process_penalties_scheduled) : '✗ Not scheduled') . '<br>';

        // Test 4: Test penalty functions
        echo '<h4>4. Penalty Functions Test</h4>';
        $test_user_id = get_current_user_id();
        $current_penalty = AuctionPenaltySystem::get_user_penalty_level($test_user_id);
        echo "Current user penalty level: $current_penalty<br>";
        echo "Penalty description: " . AuctionPenaltySystem::get_penalty_description($current_penalty) . '<br>';

        // Test bidding eligibility
        $deposit_requirements = new AuctionDepositRequirements();
        $can_bid = $deposit_requirements->can_user_bid($test_user_id);
        echo "Can user bid: " . ($can_bid ? '✓ Yes' : '✗ No') . '<br>';

        // Test filter
        $filter_result = apply_filters('auctions_for_woocommerce_user_can_bid', true, null, wp_get_current_user());
        echo "Filter result: " . ($filter_result ? '✓ Yes' : '✗ No') . '<br>';

        // Test 5: Admin menu
        echo '<h4>5. Admin Menu Test</h4>';
        $admin_url = admin_url('admin.php?page=auction-penalty');
        echo "Admin interface URL: <a href='$admin_url' target='_blank'>$admin_url</a><br>";

        // Test 6: Manual cron trigger
        echo '<h4>6. Manual Cron Test</h4>';
        $cron_check_url = home_url('/?auction-penalty-cron=check-winners');
        $cron_process_url = home_url('/?auction-penalty-cron=process-penalties');
        echo "Manual cron trigger URLs:<br>";
        echo "Check winners: <a href='$cron_check_url' target='_blank'>$cron_check_url</a><br>";
        echo "Process penalties: <a href='$cron_process_url' target='_blank'>$cron_process_url</a><br>";

        echo '<p><strong>Test completed!</strong> If you see any ✗ marks, there might be issues that need attention.</p>';
        echo '<p><em>Note: You can test the cron jobs manually by clicking the URLs above. They should return "OK" if working properly.</em></p>';
        echo '</div>';
    }
}
add_action('wp_footer', 'auction_penalty_debug_test');
add_action('admin_footer', 'auction_penalty_debug_test');
