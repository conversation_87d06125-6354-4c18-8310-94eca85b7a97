<?php
/**
 * Auction product add to cart
 *
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}


global $product, $post;

if ( ! $product->is_purchasable() || ! $product->is_sold_individually() || ! $product->is_in_stock() || $product->is_closed() ) {
	return;
}

?>

<?php do_action( 'woocommerce_before_add_to_cart_form' ); ?>

<form class="buy-now cart" method="post" enctype='multipart/form-data' data-product_id="<?php echo intval( $post->ID ); ?>">
	<?php

		do_action( 'woocommerce_before_add_to_cart_button' );

	if ( ! $product->is_sold_individually() ) {
					woocommerce_quantity_input(
						array(
							'min_value' => apply_filters( 'woocommerce_quantity_input_min', 1, $product ),
							'max_value' => apply_filters( 'woocommerce_quantity_input_max', $product->backorders_allowed() ? '' : $product->get_stock_quantity(), $product ),
						)
					);
	}
	?>
 
	<input type="hidden" name="add-to-cart" value="<?php echo esc_attr( $product->get_auction_id() ); ?>" />

	<button type="submit" class="single_add_to_cart_button button alt buy-now-button">
		Hemen Al
	</button>
	<span class="buy-now-price">
		Fiyatı :
		<?php echo wc_price( $product->get_regular_price() ); ?>
	</span>
	<span id="buy-now-info">
		<svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
		<path d="M7.99966 0.5C3.58197 0.5 0 4.08197 0 8.49966C0 12.9174 3.58197 16.5 7.99966 16.5C12.4174 16.5 16 12.9174 16 8.49966C16 4.08197 12.4174 0.5 7.99966 0.5ZM9.66502 12.8984C9.25325 13.0609 8.92546 13.1842 8.67962 13.2695C8.43445 13.3549 8.14933 13.3975 7.82493 13.3975C7.32648 13.3975 6.93841 13.2756 6.6621 13.0325C6.38578 12.7894 6.2483 12.4812 6.2483 12.1067C6.2483 11.9611 6.25845 11.8121 6.27877 11.6604C6.29977 11.5087 6.33295 11.338 6.37833 11.1463L6.89371 9.3259C6.93909 9.15117 6.97837 8.98525 7.00952 8.83084C7.04068 8.67507 7.05558 8.53217 7.05558 8.40214C7.05558 8.17052 7.00749 8.00798 6.912 7.91655C6.81515 7.82512 6.63297 7.78042 6.3614 7.78042C6.22866 7.78042 6.09185 7.80006 5.95166 7.84138C5.81283 7.88404 5.69228 7.92265 5.5934 7.96057L5.72952 7.39981C6.06679 7.26233 6.38984 7.14449 6.69799 7.04696C7.00614 6.94808 7.29735 6.89932 7.57164 6.89932C8.06671 6.89932 8.44868 7.01987 8.71754 7.25826C8.98506 7.49733 9.11983 7.80819 9.11983 8.19016C9.11983 8.2694 9.11035 8.40891 9.09206 8.60802C9.07378 8.80781 9.03924 8.98999 8.98912 9.15727L8.47644 10.9723C8.43445 11.1179 8.39721 11.2845 8.36334 11.4708C8.33016 11.657 8.3139 11.7992 8.3139 11.8947C8.3139 12.1358 8.36741 12.3004 8.47577 12.3877C8.58277 12.4751 8.77037 12.5191 9.03585 12.5191C9.16114 12.5191 9.30133 12.4968 9.45981 12.4534C9.61693 12.4101 9.73071 12.3715 9.8025 12.3383L9.66502 12.8984ZM9.57426 5.53128C9.3352 5.75342 9.04737 5.86449 8.71077 5.86449C8.37486 5.86449 8.08499 5.75342 7.84389 5.53128C7.60415 5.30914 7.48292 5.03892 7.48292 4.72332C7.48292 4.4084 7.60483 4.1375 7.84389 3.91333C8.08499 3.68849 8.37486 3.57674 8.71077 3.57674C9.04737 3.57674 9.33587 3.68849 9.57426 3.91333C9.81333 4.1375 9.93321 4.4084 9.93321 4.72332C9.93321 5.0396 9.81333 5.30914 9.57426 5.53128Z" fill="#050632"/>
		</svg>
	</span>

	<div style="display: none;">
		<input type="hidden" name="add-to-cart" value="<?php echo intval( $product->get_auction_id() ); ?>" />
		<input type="hidden" name="product_id" value="<?php echo intval( $product->get_auction_id() ); ?>" />
	</div>

	<?php do_action( 'woocommerce_after_add_to_cart_button' ); ?>

</form>

<?php do_action( 'woocommerce_after_add_to_cart_form' ); ?>
