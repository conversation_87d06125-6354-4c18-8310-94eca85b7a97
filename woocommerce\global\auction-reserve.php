<?php
/**
 * Auction reserve template
 *
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

global  $product;


if ( $product->get_type() !== 'auction' ) {
	return;
}
if ( $product->get_auction_sealed() === 'yes' || ! $product->is_reserved() ) {
	return;
}

if ( ( $product->is_reserved() === true ) && ( $product->is_reserve_met() === false ) ) : ?>
	<p class="reserve hold"  data-auction-id="<?php echo intval( $product->get_auction_id() ); ?>" ><?php echo wp_kses_post( apply_filters( 'reserve_bid_text', esc_html__( 'Reserve price has not been met', 'auctions-for-woocommerce' ) ) ); ?></p>
<?php endif; ?>
<?php if ( ( $product->is_reserved() === true ) && ( $product->is_reserve_met() === true ) ) : ?>
	<p class="reserve free"  data-auction-id="<?php echo intval( $product->get_auction_id() ); ?>"><?php echo wp_kses_post( apply_filters( 'reserve_met_bid_text', esc_html__( 'Reserve price has been met', 'auctions-for-woocommerce' ) ) ); ?></p>
<?php endif; ?>
