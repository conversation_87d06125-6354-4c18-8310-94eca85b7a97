jQuery(document).ready(function($) {
    // Initialize main gallery slider
    $('.olb-product-gallery-main').slick({
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: false,
        fade: true,
        asNavFor: '.olb-product-gallery-thumbs',
        adaptiveHeight: true,
        lazyLoad: 'ondemand',
        responsive: [
            {
                breakpoint: 768,
                settings: {
                    arrows: true,
                    prevArrow: $('.olb-gallery-prev'),
                    nextArrow: $('.olb-gallery-next')
                }
            }
        ]
    });
    
    // Initialize thumbnails slider
    $('.olb-product-gallery-thumbs').slick({
        slidesToShow: 4,
        slidesToScroll: 1,
        asNavFor: '.olb-product-gallery-main',
        dots: false,
        arrows: false,
        centerMode: false,
        focusOnSelect: true,
        infinite: false,
        responsive: [
            {
                breakpoint: 991,
                settings: {
                    slidesToShow: 6
                }
            },
            {
                breakpoint: 768,
                settings: {
                    slidesToShow: 4
                }
            },
            {
                breakpoint: 480,
                settings: {
                    slidesToShow: 3
                }
            }
        ]
    });
    
    // Custom navigation buttons
    $('.olb-gallery-prev').on('click', function() {
        $('.olb-product-gallery-main').slick('slickPrev');
    });
    
    $('.olb-gallery-next').on('click', function() {
        $('.olb-product-gallery-main').slick('slickNext');
    });
    
    // Initialize Fancybox for lightbox - updated for Fancybox v4
    try {
        Fancybox.bind('[data-fancybox="product-gallery"]', {
            // Fancybox v4 options
            dragToClose: true,
            wheel: false,
            on: {
                initLayout: (fancybox) => {
                    // Create elements
                    const $toolbar = document.createElement("div");
                    $toolbar.classList.add("fancybox__toolbar");
                    
                    fancybox.$container.prepend($toolbar);
                }
            }
        });
        
        console.log('Fancybox initialized successfully');
    } catch (error) {
        console.error('Fancybox initialization error:', error);
        
        // Fallback to simple lightbox behavior if Fancybox fails
        $('.olb-gallery-item a').on('click', function(e) {
            e.preventDefault();
            const imgSrc = $(this).attr('href');
            
            // Create a simple lightbox
            $('body').append('<div class="simple-lightbox"><div class="simple-lightbox-content"><img src="' + imgSrc + '"><button class="close-lightbox">×</button></div></div>');
            
            // Close lightbox on click
            $('.simple-lightbox, .close-lightbox').on('click', function() {
                $('.simple-lightbox').remove();
            });
            
            // Prevent propagation
            $('.simple-lightbox-content').on('click', function(e) {
                e.stopPropagation();
            });
        });
    }
    
    // Prevent default click behavior on mobile
    $('.olb-product-gallery-main .olb-gallery-item a').on('click', function(e) {
        if (window.innerWidth <= 768) {
            e.preventDefault();
        }
    });
    
    // Add touch swipe support
    $('.olb-product-gallery-main').on('swipe', function(event, slick, direction) {
        if (direction === 'left') {
            $('.olb-product-gallery-main').slick('slickNext');
        } else {
            $('.olb-product-gallery-main').slick('slickPrev');
        }
    });
    
    // Reinitialize gallery on variation change
    $(document).on('found_variation', 'form.variations_form', function(event, variation) {
        $('.olb-product-gallery-main').slick('setPosition');
        $('.olb-product-gallery-thumbs').slick('setPosition');
    });
    
    // Reinitialize sliders on window resize
    $(window).on('resize', function() {
        $('.olb-product-gallery-main').slick('setPosition');
        $('.olb-product-gallery-thumbs').slick('setPosition');
    });
}); 