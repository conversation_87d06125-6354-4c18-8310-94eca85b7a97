<?php
/**
 * Auction Deposit Handler
 * 
 * Handles deposit product pricing, checkout process, and payment tracking
 * 
 * @package AuctionPenaltySystem
 * @version 1.0.0
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Auction Deposit Handler Class
 */
class AuctionDepositHandler {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Custom pricing for deposit products
        add_filter('woocommerce_product_get_price', array($this, 'custom_deposit_price'), 10, 2);
        add_filter('woocommerce_product_get_regular_price', array($this, 'custom_deposit_price'), 10, 2);
        add_action('woocommerce_before_calculate_totals', array($this, 'set_cart_item_prices'));
        
        // Add custom fields to checkout
        add_action('woocommerce_checkout_fields', array($this, 'add_checkout_fields'));
        add_action('woocommerce_checkout_create_order', array($this, 'save_checkout_fields_to_order'), 10, 2);
        add_action('woocommerce_checkout_update_order_meta', array($this, 'save_checkout_fields'));
        add_action('woocommerce_new_order', array($this, 'save_checkout_fields'));
        
        // No need for separate processing - order meta is enough!
        
        // Add custom thank you page content
        add_action('woocommerce_thankyou', array($this, 'custom_thankyou_content'), 5, 1);
        
        // Display auction product info on deposit product page
        add_action('woocommerce_single_product_summary', array($this, 'display_auction_info'), 15);

        // Clear cart before adding deposit product
        add_action('init', array($this, 'handle_deposit_add_to_cart'));

        // Customize cart item display
        add_filter('woocommerce_cart_item_name', array($this, 'customize_cart_item_name'), 10, 3);
        add_filter('woocommerce_checkout_cart_item_quantity', array($this, 'customize_checkout_item_quantity'), 10, 3);
        add_filter('woocommerce_cart_item_price', array($this, 'customize_cart_item_price'), 10, 3);

        // Save cart item meta to order
        add_action('woocommerce_checkout_create_order_line_item', array($this, 'save_cart_item_meta_to_order'), 10, 4);
    }
    
    /**
     * Custom pricing for deposit products based on URL parameters or session
     */
    public function custom_deposit_price($price, $product) {
        // Check if this is a deposit product
        if (!$this->is_deposit_product($product)) {
            return $price;
        }

        // Get custom price from URL parameters (for add-to-cart)
        $custom_price = isset($_GET['deposit_amount']) ? floatval($_GET['deposit_amount']) : 0;

        // If no URL parameter, check if we're in cart/checkout and get from session
        if ($custom_price == 0 && WC()->session) {
            $custom_price = WC()->session->get('deposit_amount_' . $product->get_id(), 0);
        }

        // Store in session for later use
        if ($custom_price > 0 && WC()->session) {
            WC()->session->set('deposit_amount_' . $product->get_id(), $custom_price);
        }

        if ($custom_price > 0) {
            return $custom_price;
        }

        return $price;
    }

    /**
     * Set cart item prices for deposit products
     */
    public function set_cart_item_prices($cart) {
        if (is_admin() && !defined('DOING_AJAX')) {
            return;
        }

        foreach ($cart->get_cart() as $cart_item_key => $cart_item) {
            if (isset($cart_item['is_deposit']) && $cart_item['is_deposit']) {
                $deposit_amount = isset($cart_item['deposit_amount']) ? floatval($cart_item['deposit_amount']) : 0;
                if ($deposit_amount > 0) {
                    $cart_item['data']->set_price($deposit_amount);
                }
            }
        }
    }

    /**
     * Handle deposit add to cart - clear cart first then add deposit product
     */
    public function handle_deposit_add_to_cart() {
        if (isset($_GET['add-to-cart']) && isset($_GET['auction_product_id'])) {
            $product_id = intval($_GET['add-to-cart']);
            $product = wc_get_product($product_id);

            if ($product && $this->is_deposit_product($product)) {
                // DEBUG
                if (current_user_can('manage_options')) {
                    error_log("DEPOSIT ADD TO CART: Product ID: $product_id, Auction ID: " . $_GET['auction_product_id'] . ", Amount: " . $_GET['deposit_amount']);
                }

                // Clear cart before adding deposit product
                if (WC()->cart) {
                    WC()->cart->empty_cart();

                    // Add deposit product to cart
                    $cart_item_data = array(
                        'auction_product_id' => intval($_GET['auction_product_id']),
                        'deposit_amount' => floatval($_GET['deposit_amount']),
                        'is_deposit' => true
                    );

                    $cart_item_key = WC()->cart->add_to_cart($product_id, 1, 0, array(), $cart_item_data);

                    // DEBUG
                    if (current_user_can('manage_options')) {
                        error_log("CART ITEM KEY: " . $cart_item_key);
                        error_log("CART CONTENTS: " . print_r(WC()->cart->get_cart(), true));
                    }

                    // Store auction info in session
                    WC()->session->set('auction_product_id', intval($_GET['auction_product_id']));
                    WC()->session->set('deposit_amount_' . $product_id, floatval($_GET['deposit_amount']));

                    // Redirect to checkout to avoid URL parameter issues
                    wp_redirect(wc_get_checkout_url());
                    exit;
                }
            }
        }
    }

    /**
     * Customize cart item name for deposit products
     */
    public function customize_cart_item_name($product_name, $cart_item, $cart_item_key) {
        if (isset($cart_item['is_deposit']) && $cart_item['is_deposit']) {
            $auction_product_id = isset($cart_item['auction_product_id']) ? $cart_item['auction_product_id'] : 0;
            $auction_product = $auction_product_id ? wc_get_product($auction_product_id) : null;

            if ($auction_product) {
                $penalty_level = AuctionPenaltySystem::get_user_penalty_level(get_current_user_id());
                $percentage = $penalty_level == 1 ? '10%' : '40%';

                $product_name = sprintf(
                    __('Auction Deposit (%s) for: %s', 'woocommerce'),
                    $percentage,
                    $auction_product->get_name()
                );
            }
        }

        return $product_name;
    }

    /**
     * Customize checkout item quantity display
     */
    public function customize_checkout_item_quantity($quantity, $cart_item, $cart_item_key) {
        if (isset($cart_item['is_deposit']) && $cart_item['is_deposit']) {
            return '<strong>1</strong>';
        }

        return $quantity;
    }

    /**
     * Customize cart item price display
     */
    public function customize_cart_item_price($price, $cart_item, $cart_item_key) {
        if (isset($cart_item['is_deposit']) && $cart_item['is_deposit']) {
            $deposit_amount = isset($cart_item['deposit_amount']) ? $cart_item['deposit_amount'] : 0;
            if ($deposit_amount > 0) {
                return wc_price($deposit_amount);
            }
        }

        return $price;
    }

    /**
     * Save cart item meta data to order line item
     */
    public function save_cart_item_meta_to_order($item, $cart_item_key, $values, $order) {
        if (isset($values['is_deposit']) && $values['is_deposit']) {
            $item->add_meta_data('is_deposit', 'yes');

            if (isset($values['auction_product_id'])) {
                $item->add_meta_data('auction_product_id', $values['auction_product_id']);
            }

            if (isset($values['deposit_amount'])) {
                $item->add_meta_data('deposit_amount', $values['deposit_amount']);
            }
        }
    }

    /**
     * Check if product is a deposit product
     */
    private function is_deposit_product($product) {
        if (!$product) {
            return false;
        }
        
        $slug = $product->get_slug();
        return in_array($slug, array('seviye-1-depozito', 'seviye-2-depozito'));
    }
    
    /**
     * Add custom fields to checkout for deposit products
     */
    public function add_checkout_fields($fields) {
        // DEBUG: Always show debug info for now
        if (current_user_can('manage_options')) {
            echo '<div style="background: yellow; padding: 10px; margin: 10px;">';
            echo '<strong>DEBUG CHECKOUT FIELDS:</strong><br>';
            echo 'Cart contains deposit: ' . ($this->cart_contains_deposit_product() ? 'YES' : 'NO') . '<br>';

            if (WC()->cart) {
                foreach (WC()->cart->get_cart() as $key => $cart_item) {
                    echo 'Cart Item: ' . $cart_item['data']->get_name() . '<br>';
                    echo 'Is Deposit: ' . (isset($cart_item['is_deposit']) ? 'YES' : 'NO') . '<br>';
                    echo 'Auction Product ID: ' . (isset($cart_item['auction_product_id']) ? $cart_item['auction_product_id'] : 'NOT SET') . '<br>';
                    echo 'Deposit Amount: ' . (isset($cart_item['deposit_amount']) ? $cart_item['deposit_amount'] : 'NOT SET') . '<br>';
                    echo '---<br>';
                }
            }
            echo '</div>';
        }

        // Check if cart contains deposit product
        if (!$this->cart_contains_deposit_product()) {
            return $fields;
        }

        // Get auction product ID from cart item data
        $auction_product_id = 0;
        $auction_product = null;

        foreach (WC()->cart->get_cart() as $cart_item) {
            if (isset($cart_item['is_deposit']) && $cart_item['is_deposit']) {
                $auction_product_id = isset($cart_item['auction_product_id']) ? $cart_item['auction_product_id'] : 0;
                $auction_product = $auction_product_id ? wc_get_product($auction_product_id) : null;
                break;
            }
        }

        if ($auction_product_id && $auction_product) {
            // Add a visible notice about the deposit
            add_action('woocommerce_checkout_before_customer_details', function() use ($auction_product) {
                echo '<div class="woocommerce-info">';
                echo '<strong>' . __('Deposit Payment Notice:', 'woocommerce') . '</strong><br>';
                echo sprintf(__('You are paying a deposit for the auction: %s', 'woocommerce'), $auction_product->get_name());
                echo '</div>';
            });

            $fields['billing']['auction_product_info'] = array(
                'type' => 'select',
                'label' => __('Deposit for Auction Product', 'woocommerce'),
                'required' => true,
                'options' => array(
                    $auction_product_id => $auction_product->get_name()
                ),
                'default' => $auction_product_id,
                'custom_attributes' => array('readonly' => 'readonly'),
                'description' => __('This deposit payment is for the selected auction product only.', 'woocommerce')
            );
        }

        return $fields;
    }
    
    /**
     * Save checkout fields to order (new method)
     */
    public function save_checkout_fields_to_order($order, $data) {
        $auction_product_id = 0;

        // Try to get from POST first
        if (isset($_POST['auction_product_info'])) {
            $auction_product_id = intval($_POST['auction_product_info']);
            error_log("SAVE CHECKOUT NEW: Got auction_product_id from POST: $auction_product_id");
        }
        // If not in POST, get from order items (cart item data)
        else {
            foreach ($order->get_items() as $item) {
                $auction_product_id = $item->get_meta('auction_product_id');
                if ($auction_product_id) {
                    error_log("SAVE CHECKOUT NEW: Got auction_product_id from item meta: $auction_product_id");
                    break;
                }
            }
        }

        if ($auction_product_id) {
            $order->update_meta_data('_auction_product_id', $auction_product_id);
            $order->update_meta_data('_is_deposit_order', 'yes');
            error_log("SAVE CHECKOUT NEW: Saved order meta - auction: $auction_product_id");
        } else {
            error_log("SAVE CHECKOUT NEW: No auction_product_id found");
        }
    }

    /**
     * Save checkout fields (old method - keep as backup)
     */
    public function save_checkout_fields($order_id) {
        $auction_product_id = 0;

        // Try to get from POST first
        if (isset($_POST['auction_product_info'])) {
            $auction_product_id = intval($_POST['auction_product_info']);
            error_log("SAVE CHECKOUT OLD: Got auction_product_id from POST: $auction_product_id");
        }
        // If not in POST, get from order items (cart item data)
        else {
            $order = wc_get_order($order_id);
            if ($order) {
                foreach ($order->get_items() as $item) {
                    $auction_product_id = $item->get_meta('auction_product_id');
                    if ($auction_product_id) {
                        error_log("SAVE CHECKOUT OLD: Got auction_product_id from item meta: $auction_product_id");
                        break;
                    }
                }
            }
        }

        if ($auction_product_id) {
            update_post_meta($order_id, '_auction_product_id', $auction_product_id);
            update_post_meta($order_id, '_is_deposit_order', 'yes');
            error_log("SAVE CHECKOUT OLD: Saved order meta for order $order_id - auction: $auction_product_id");
        } else {
            error_log("SAVE CHECKOUT OLD: No auction_product_id found for order $order_id");
        }
    }

    /**
     * Get deposit product ID from order
     */
    private function get_deposit_product_id_from_order($order_id) {
        $order = wc_get_order($order_id);
        if (!$order) {
            return 0;
        }

        foreach ($order->get_items() as $item) {
            $product = $item->get_product();
            if ($this->is_deposit_product($product)) {
                return $product->get_id();
            }
        }

        return 0;
    }

    // Removed - no need for separate processing!
    
    // Removed - using WooCommerce orders instead of custom table!
    
    /**
     * Check if cart contains deposit product
     */
    private function cart_contains_deposit_product() {
        if (!WC()->cart) {
            return false;
        }
        
        foreach (WC()->cart->get_cart() as $cart_item) {
            $product = $cart_item['data'];
            if ($this->is_deposit_product($product)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Custom thank you page content for deposit orders
     */
    public function custom_thankyou_content($order_id) {
        $order = wc_get_order($order_id);

        if (!$order) {
            return;
        }

        // DEBUG: Show debug info
        if (current_user_can('manage_options')) {
            echo '<div style="background: yellow; padding: 10px; margin: 10px;">';
            echo '<strong>DEBUG THANK YOU PAGE:</strong><br>';
            echo 'Order ID: ' . $order_id . '<br>';
            echo 'Is Deposit Order: ' . get_post_meta($order_id, '_is_deposit_order', true) . '<br>';
            echo 'Auction Product ID: ' . get_post_meta($order_id, '_auction_product_id', true) . '<br>';

            // Check order items
            foreach ($order->get_items() as $item) {
                echo 'Order Item: ' . $item->get_name() . '<br>';
                $meta_data = $item->get_meta_data();
                foreach ($meta_data as $meta) {
                    echo 'Meta: ' . $meta->key . ' = ' . $meta->value . '<br>';
                }
            }
            echo '</div>';
        }

        // Check if this is a deposit order - try multiple ways
        $is_deposit_order = get_post_meta($order_id, '_is_deposit_order', true);
        $auction_product_id = get_post_meta($order_id, '_auction_product_id', true);

        // If not found in order meta, check order items
        if (!$auction_product_id) {
            foreach ($order->get_items() as $item) {
                $auction_product_id = $item->get_meta('auction_product_id');
                if ($auction_product_id) {
                    $is_deposit_order = 'yes';
                    break;
                }
            }
        }

        // Also check if any order item is a deposit product
        if (!$is_deposit_order) {
            foreach ($order->get_items() as $item) {
                $product = $item->get_product();
                if ($product && $this->is_deposit_product($product)) {
                    $is_deposit_order = 'yes';
                    break;
                }
            }
        }

        if ($is_deposit_order === 'yes') {
            $auction_product = $auction_product_id ? wc_get_product($auction_product_id) : null;

            echo '<div class="deposit-success-message">';
            echo '<h3>' . __('Aşağıdaki ürün için Deposit Ödemeniz Başarıyla Gerçekleştirilmiştir', 'woocommerce') . '</h3>';
            echo '<p>' . __('Artık bu ürüne teklif verebilirsiniz.', 'woocommerce') . '</p>';

            if ($auction_product) {
                echo '<p><strong>' . __('Ürün:', 'woocommerce') . '</strong> ' . $auction_product->get_name() . '</p>';
                echo '<a href="' . get_permalink($auction_product_id) . '" class="button">';
                echo __('Teklif Ver', 'woocommerce');
                echo '</a>';
            } else {
                echo '<p>' . __('Ürün bilgileri bulunamadı. Lütfen iletişime geçin.', 'woocommerce') . '</p>';
            }

            echo '</div>';

            echo '<style>
            .deposit-success-message {
                background: #d4edda;
                border: 1px solid #c3e6cb;
                color: #155724;
                padding: 20px;
                border-radius: 5px;
                margin: 20px 0;
                text-align: center;
            }
            .deposit-success-message h3 {
                margin-top: 0;
                color: #155724;
            }
            .deposit-success-message .button {
                background-color: #28a745;
                color: white;
                padding: 12px 24px;
                text-decoration: none;
                border-radius: 5px;
                display: inline-block;
                margin-top: 15px;
            }
            .deposit-success-message .button:hover {
                background-color: #218838;
                color: white;
            }
            </style>';
        }
    }
    
    /**
     * Display auction product info on deposit product page
     */
    public function display_auction_info() {
        global $product;
        
        if (!$this->is_deposit_product($product)) {
            return;
        }
        
        $auction_product_id = isset($_GET['auction_product_id']) ? intval($_GET['auction_product_id']) : 0;
        $auction_product = $auction_product_id ? wc_get_product($auction_product_id) : null;
        
        if ($auction_product) {
            echo '<div class="auction-deposit-info">';
            echo '<h4>' . __('Deposit for Auction Product', 'woocommerce') . '</h4>';
            echo '<p><strong>' . __('Auction Product:', 'woocommerce') . '</strong> ' . $auction_product->get_name() . '</p>';
            echo '<p><strong>' . __('Deposit Amount:', 'woocommerce') . '</strong> ' . wc_price($product->get_price()) . '</p>';
            echo '<p>' . __('After completing this deposit payment, you will be able to bid on the auction product.', 'woocommerce') . '</p>';
            echo '</div>';
            
            echo '<style>
            .auction-deposit-info {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                padding: 15px;
                border-radius: 5px;
                margin: 15px 0;
            }
            .auction-deposit-info h4 {
                margin-top: 0;
                color: #495057;
            }
            </style>';
        }
    }
}
