<?php
/**
 * Auction Deposit Handler
 * 
 * Handles deposit product pricing, checkout process, and payment tracking
 * 
 * @package AuctionPenaltySystem
 * @version 1.0.0
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Auction Deposit Handler Class
 */
class AuctionDepositHandler {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Custom pricing for deposit products
        add_filter('woocommerce_product_get_price', array($this, 'custom_deposit_price'), 10, 2);
        add_filter('woocommerce_product_get_regular_price', array($this, 'custom_deposit_price'), 10, 2);
        
        // Add custom fields to checkout
        add_action('woocommerce_checkout_fields', array($this, 'add_checkout_fields'));
        add_action('woocommerce_checkout_update_order_meta', array($this, 'save_checkout_fields'));
        
        // Process deposit payment completion
        add_action('woocommerce_order_status_processing', array($this, 'process_deposit_payment'), 10, 1);
        add_action('woocommerce_order_status_completed', array($this, 'process_deposit_payment'), 10, 1);
        
        // Add custom thank you page content
        add_action('woocommerce_thankyou', array($this, 'custom_thankyou_content'), 10, 1);
        
        // Display auction product info on deposit product page
        add_action('woocommerce_single_product_summary', array($this, 'display_auction_info'), 15);
    }
    
    /**
     * Custom pricing for deposit products based on URL parameters
     */
    public function custom_deposit_price($price, $product) {
        // Check if this is a deposit product
        if (!$this->is_deposit_product($product)) {
            return $price;
        }
        
        // Get custom price from URL parameters
        $custom_price = isset($_GET['deposit_amount']) ? floatval($_GET['deposit_amount']) : 0;
        
        if ($custom_price > 0) {
            return $custom_price;
        }
        
        return $price;
    }
    
    /**
     * Check if product is a deposit product
     */
    private function is_deposit_product($product) {
        if (!$product) {
            return false;
        }
        
        $slug = $product->get_slug();
        return in_array($slug, array('seviye-1-depozito', 'seviye-2-depozito'));
    }
    
    /**
     * Add custom fields to checkout for deposit products
     */
    public function add_checkout_fields($fields) {
        // Check if cart contains deposit product
        if (!$this->cart_contains_deposit_product()) {
            return $fields;
        }
        
        $auction_product_id = isset($_GET['auction_product_id']) ? intval($_GET['auction_product_id']) : 0;
        $auction_product = $auction_product_id ? wc_get_product($auction_product_id) : null;
        
        $fields['billing']['auction_product_info'] = array(
            'type' => 'select',
            'label' => __('Deposit for Auction Product', 'woocommerce'),
            'required' => true,
            'options' => array(
                $auction_product_id => $auction_product ? $auction_product->get_name() : __('Unknown Product', 'woocommerce')
            ),
            'default' => $auction_product_id,
            'custom_attributes' => array('readonly' => 'readonly'),
            'description' => __('This deposit payment is for the selected auction product only.', 'woocommerce')
        );
        
        return $fields;
    }
    
    /**
     * Save checkout fields
     */
    public function save_checkout_fields($order_id) {
        if (isset($_POST['auction_product_info'])) {
            $auction_product_id = intval($_POST['auction_product_info']);
            update_post_meta($order_id, '_auction_product_id', $auction_product_id);
            update_post_meta($order_id, '_is_deposit_order', 'yes');
        }
    }
    
    /**
     * Process deposit payment when order is completed/processing
     */
    public function process_deposit_payment($order_id) {
        $order = wc_get_order($order_id);
        
        if (!$order) {
            return;
        }
        
        // Check if this is a deposit order
        $is_deposit_order = get_post_meta($order_id, '_is_deposit_order', true);
        if ($is_deposit_order !== 'yes') {
            return;
        }
        
        // Get auction product ID
        $auction_product_id = get_post_meta($order_id, '_auction_product_id', true);
        if (!$auction_product_id) {
            return;
        }
        
        $user_id = $order->get_customer_id();
        if (!$user_id) {
            return;
        }
        
        // Record deposit payment
        $this->record_deposit_payment($user_id, $auction_product_id, $order_id, $order->get_total());
        
        error_log("Auction Deposit: Recorded deposit payment for user {$user_id}, auction {$auction_product_id}, order {$order_id}");
    }
    
    /**
     * Record deposit payment in database
     */
    private function record_deposit_payment($user_id, $auction_product_id, $order_id, $amount) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'auction_deposits';
        
        // Create table if it doesn't exist
        $this->create_deposits_table();
        
        // Check if already recorded
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE order_id = %d",
            $order_id
        ));
        
        if (!$existing) {
            $wpdb->insert(
                $table_name,
                array(
                    'user_id' => $user_id,
                    'auction_product_id' => $auction_product_id,
                    'order_id' => $order_id,
                    'amount' => $amount,
                    'status' => 'completed',
                    'date_created' => current_time('mysql')
                ),
                array('%d', '%d', '%d', '%f', '%s', '%s')
            );
        }
    }
    
    /**
     * Create deposits table
     */
    private function create_deposits_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'auction_deposits';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            auction_product_id bigint(20) NOT NULL,
            order_id bigint(20) NOT NULL,
            amount decimal(10,2) NOT NULL,
            status varchar(20) DEFAULT 'pending',
            date_created datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY auction_product_id (auction_product_id),
            KEY order_id (order_id),
            UNIQUE KEY unique_deposit (user_id, auction_product_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Check if cart contains deposit product
     */
    private function cart_contains_deposit_product() {
        if (!WC()->cart) {
            return false;
        }
        
        foreach (WC()->cart->get_cart() as $cart_item) {
            $product = $cart_item['data'];
            if ($this->is_deposit_product($product)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Custom thank you page content for deposit orders
     */
    public function custom_thankyou_content($order_id) {
        $order = wc_get_order($order_id);
        
        if (!$order) {
            return;
        }
        
        // Check if this is a deposit order
        $is_deposit_order = get_post_meta($order_id, '_is_deposit_order', true);
        if ($is_deposit_order !== 'yes') {
            return;
        }
        
        $auction_product_id = get_post_meta($order_id, '_auction_product_id', true);
        $auction_product = $auction_product_id ? wc_get_product($auction_product_id) : null;
        
        if ($auction_product) {
            echo '<div class="deposit-success-message">';
            echo '<h3>' . __('Deposit Payment Successful!', 'woocommerce') . '</h3>';
            echo '<p>' . __('Your deposit has been processed successfully. You can now bid on the auction.', 'woocommerce') . '</p>';
            echo '<p><strong>' . __('Auction Product:', 'woocommerce') . '</strong> ' . $auction_product->get_name() . '</p>';
            echo '<a href="' . get_permalink($auction_product_id) . '" class="button">';
            echo __('Go to Auction & Start Bidding', 'woocommerce');
            echo '</a>';
            echo '</div>';
            
            echo '<style>
            .deposit-success-message {
                background: #d4edda;
                border: 1px solid #c3e6cb;
                color: #155724;
                padding: 20px;
                border-radius: 5px;
                margin: 20px 0;
                text-align: center;
            }
            .deposit-success-message h3 {
                margin-top: 0;
                color: #155724;
            }
            .deposit-success-message .button {
                background-color: #28a745;
                color: white;
                padding: 12px 24px;
                text-decoration: none;
                border-radius: 5px;
                display: inline-block;
                margin-top: 15px;
            }
            .deposit-success-message .button:hover {
                background-color: #218838;
                color: white;
            }
            </style>';
        }
    }
    
    /**
     * Display auction product info on deposit product page
     */
    public function display_auction_info() {
        global $product;
        
        if (!$this->is_deposit_product($product)) {
            return;
        }
        
        $auction_product_id = isset($_GET['auction_product_id']) ? intval($_GET['auction_product_id']) : 0;
        $auction_product = $auction_product_id ? wc_get_product($auction_product_id) : null;
        
        if ($auction_product) {
            echo '<div class="auction-deposit-info">';
            echo '<h4>' . __('Deposit for Auction Product', 'woocommerce') . '</h4>';
            echo '<p><strong>' . __('Auction Product:', 'woocommerce') . '</strong> ' . $auction_product->get_name() . '</p>';
            echo '<p><strong>' . __('Deposit Amount:', 'woocommerce') . '</strong> ' . wc_price($product->get_price()) . '</p>';
            echo '<p>' . __('After completing this deposit payment, you will be able to bid on the auction product.', 'woocommerce') . '</p>';
            echo '</div>';
            
            echo '<style>
            .auction-deposit-info {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                padding: 15px;
                border-radius: 5px;
                margin: 15px 0;
            }
            .auction-deposit-info h4 {
                margin-top: 0;
                color: #495057;
            }
            </style>';
        }
    }
}
