/**
 * OTP Login JavaScript
 */
(function($) {
    $(document).ready(function() {
        // Store form states
        var formStates = {
            'login': {
                otpSent: false
            },
            'register': {
                otpSent: false
            }
        };
        
        // Tab switching
        $('.olb-otp-tab').on('click', function() {
            var tab = $(this).data('tab');
            
            // Update active tab
            $('.olb-otp-tab').removeClass('active');
            $(this).addClass('active');
            
            // Show active content
            $('.olb-otp-tab-content').removeClass('active');
            $('#' + tab + '-tab').addClass('active');
            
            // Check if O<PERSON> was sent for this tab
            if (formStates[tab].otpSent) {
                var $form = $('#' + tab + '-tab').find('form');
                $form.find('.otp-row').show();
                $form.find('.send-otp-btn').hide();
                $form.find('.verify-otp-btn').show();
                $form.find('.dev-otp-display').show();
            } else {
                // Reset form if OTP was not sent
                resetForm($('#' + tab + '-tab').find('form'));
            }
            
            // Remove any messages
            $('.olb-message').remove();
        });
        
        // Function to completely reset a form
        function resetForm($form) {
            $form[0].reset();
            $form.find('.otp-row').hide();
            $form.find('.send-otp-btn')
                .show()
                .prop('disabled', false)
                .text('Devam');
            $form.find('.verify-otp-btn')
                .hide()
                .prop('disabled', false)
                .text($form.find('input[name="action_type"]').val() === 'login' ? 'Giriş Yap' : 'Üye Ol');
            $form.find('.dev-otp-display').hide();
            $form.find('.otp-digit').val('');
            $form.find('input[name="otp_code"]').val('');
            $form.find('.timer').text('');
            $form.find('.resend-link').hide();
            $form.find('.olb-message').remove();
            
            // Reset form state
            var actionType = $form.find('input[name="action_type"]').val();
            formStates[actionType].otpSent = false;
        }
        
        // Phone number validation
        $('input[type="tel"]').on('input', function() {
            var input = $(this).val().replace(/\D/g, '');
            if (input.length > 10) {
                input = input.substring(0, 10);
            }
            $(this).val(input);
        });
        
        // Handle paste event for OTP digits
        $(document).on('paste', '.otp-digit', function(e) {
            e.preventDefault();
            
            // Get pasted data
            var clipboardData = e.originalEvent.clipboardData || window.clipboardData;
            var pastedData = clipboardData.getData('text').replace(/\D/g, '');
            
            // If pasted data is 6 digits, distribute to all boxes
            if (pastedData.length === 6) {
                var $form = $(this).closest('form');
                var $digits = $form.find('.otp-digit');
                
                // Fill each digit box
                $digits.each(function(index) {
                    $(this).val(pastedData.charAt(index));
                });
                
                // Update hidden input
                updateOtpValue($form);
                
                // Trigger verification after a short delay
                setTimeout(function() {
                    $form.find('.verify-otp-btn').trigger('click');
                }, 200);
            } else {
                // If not 6 digits, just put in current box
                $(this).val(pastedData.charAt(0) || '');
                
                // Move to next box if needed
                if (pastedData.length > 0) {
                    var $next = $(this).next('.otp-digit');
                    if ($next.length) {
                        $next.focus();
                    }
                }
                
                // Update hidden input
                updateOtpValue($(this).closest('form'));
            }
        });
        
        // OTP digit input handling
        $(document).on('input', '.otp-digit', function() {
            var $this = $(this);
            var maxLength = parseInt($this.attr('maxlength'), 10);
            var value = $this.val();
            
            // Only allow numbers
            value = value.replace(/\D/g, '');
            $this.val(value);
            
            // Auto move to next input
            if (value.length >= maxLength) {
                var $next = $this.next('.otp-digit');
                if ($next.length) {
                    $next.focus();
                } else {
                    // If this is the last digit, trigger verify button click
                    var $form = $this.closest('form');
                    var otpValue = '';
                    $form.find('.otp-digit').each(function() {
                        otpValue += $(this).val();
                    });
                    
                    if (otpValue.length === 6) {
                        setTimeout(function() {
                            $form.find('.verify-otp-btn').trigger('click');
                        }, 200);
                    }
                }
            }
            
            // Update hidden input with complete OTP
            updateOtpValue($this.closest('form'));
        });
        
        // Handle backspace to go to previous input
        $(document).on('keydown', '.otp-digit', function(e) {
            var $this = $(this);
            
            // If backspace is pressed and the field is empty
            if (e.keyCode === 8 && $this.val() === '') {
                var $prev = $this.prev('.otp-digit');
                if ($prev.length) {
                    $prev.focus().val('');
                }
            }
        });
        
        // Function to update the hidden OTP input
        function updateOtpValue($form) {
            var otpValue = '';
            $form.find('.otp-digit').each(function() {
                otpValue += $(this).val();
            });
            $form.find('input[name="otp_code"]').val(otpValue);
        }
        
        // Send OTP
        $('.olb-otp-form').on('submit', function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var phoneNumber = $form.find('input[type="tel"]').val();
            var actionType = $form.find('input[name="action_type"]').val();
            
            if (!phoneNumber) {
                showMessage($form, 'Lütfen telefon numaranızı girin.', 'error');
                return;
            }
            
            // Disable button and show loading state
            var $button = $form.find('.send-otp-btn');
            $button.prop('disabled', true).text('Gönderiliyor...');
            
            // Send AJAX request
            $.ajax({
                url: olb_otp_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'olb_send_otp',
                    phone: phoneNumber,
                    type: actionType,
                    nonce: $form.find('input[name="olb_otp_nonce"]').val()
                },
                success: function(response) {
                    if (response.success) {
                        showMessage($form, response.data.message, 'success');
                        
                        // Show OTP input field
                        $form.find('.otp-row').show();
                        $button.hide();
                        $form.find('.verify-otp-btn').show();
                        
                        // Show remember me checkbox
                        $form.find('.remember-me-row').show();
                        
                        // Focus on first OTP digit
                        $form.find('.otp-digit').first().focus();
                        
                        // Start countdown timer
                        startCountdown($form);
                        
                        // For development: show OTP code
                        if (response.data.otp) {
                            console.log('OTP Code: ' + response.data.otp);
                            $form.find('.dev-otp-display').show();
                            $form.find('.dev-otp-code').text(response.data.otp);
                        }
                        
                        // Update form state
                        formStates[actionType].otpSent = true;
                    } else {
                        showMessage($form, response.data.message, 'error');
                        $button.prop('disabled', false).text('Devam');
                    }
                },
                error: function() {
                    showMessage($form, 'Bir hata oluştu. Lütfen tekrar deneyin.', 'error');
                    $button.prop('disabled', false).text('Devam');
                }
            });
        });
        
        // Verify OTP
        $('.verify-otp-btn').on('click', function() {
            var $form = $(this).closest('form');
            var phoneNumber = $form.find('input[type="tel"]').val();
            var otpCode = $form.find('input[name="otp_code"]').val();
            var actionType = $form.find('input[name="action_type"]').val();
            var rememberMe = $form.find('input[name="remember_me"]').is(':checked') ? 1 : 0;
            
            if (!otpCode || otpCode.length !== 6) {
                showMessage($form, 'Lütfen 6 haneli doğrulama kodunu girin.', 'error');
                return;
            }
            
            // Disable button and show loading state
            var $button = $(this);
            $button.prop('disabled', true).text('Doğrulanıyor...');
            
            // Send AJAX request
            $.ajax({
                url: olb_otp_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'olb_verify_otp',
                    phone: phoneNumber,
                    otp: otpCode,
                    type: actionType,
                    remember_me: rememberMe,
                    nonce: $form.find('input[name="olb_otp_nonce"]').val()
                },
                success: function(response) {
                    if (response.success) {
                        showMessage($form, response.data.message, 'success');
                        
                        // Reset form state after successful verification
                        formStates[actionType].otpSent = false;
                        
                        // Redirect after successful login/register
                        setTimeout(function() {
                            window.location.href = response.data.redirect;
                        }, 1000);
                    } else {
                        showMessage($form, response.data.message, 'error');
                        $button.prop('disabled', false).text(actionType === 'login' ? 'Giriş Yap' : 'Üye Ol');
                    }
                },
                error: function() {
                    showMessage($form, 'Bir hata oluştu. Lütfen tekrar deneyin.', 'error');
                    $button.prop('disabled', false).text(actionType === 'login' ? 'Giriş Yap' : 'Üye Ol');
                }
            });
        });
        
        // Resend OTP
        $(document).on('click', '.resend-link', function(e) {
            e.preventDefault();
            
            var $form = $(this).closest('form');
            var phoneNumber = $form.find('input[type="tel"]').val();
            var actionType = $form.find('input[name="action_type"]').val();
            
            // Hide resend link
            $(this).hide();
            
            // Send AJAX request
            $.ajax({
                url: olb_otp_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'olb_send_otp',
                    phone: phoneNumber,
                    type: actionType,
                    nonce: $form.find('input[name="olb_otp_nonce"]').val()
                },
                success: function(response) {
                    if (response.success) {
                        showMessage($form, response.data.message, 'success');
                        
                        // Reset and start countdown timer
                        startCountdown($form);
                        
                        // For development: show OTP code
                        if (response.data.otp) { 
                            $form.find('.dev-otp-display').show();
                            $form.find('.dev-otp-code').text(response.data.otp);
                        }
                    } else {
                        showMessage($form, response.data.message, 'error');
                        $form.find('.resend-link').show();
                    }
                },
                error: function() {
                    showMessage($form, 'Bir hata oluştu. Lütfen tekrar deneyin.', 'error');
                    $form.find('.resend-link').show();
                }
            });
        });
        
        // Helper function to show messages
        function showMessage($form, message, type) {
            // Remove any existing messages
            $form.find('.olb-message').remove();
            
            // Create new message
            var $message = $('<div class="olb-message ' + type + '">' + message + '</div>');
            
            // Add to the form
            $form.prepend($message);
            
            // Auto-hide after 5 seconds
            setTimeout(function() {
                $message.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 5000);
        }
        
        // Countdown timer for OTP resend
        function startCountdown($form) {
            var seconds = 60;
            var $timer = $form.find('.timer');
            var $resendLink = $form.find('.resend-link');
            
            $timer.text('Tekrar gönderebilmek için ' + seconds + ' saniye bekleyin');
            
            var interval = setInterval(function() {
                seconds--;
                
                if (seconds <= 0) {
                    clearInterval(interval);
                    $timer.text('');
                    $resendLink.show();
                } else {
                    $timer.text('Tekrar gönderebilmek için ' + seconds + ' saniye bekleyin');
                }
            }, 1000);
        }
    });
})(jQuery); 