<?php
/**
 * Auction bid form template
 *
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

global $product;

if ( ! ( $product && $product->get_type() == 'auction' ) ) {
	return;
}
$product_id       = $product->get_auction_id();
$user_max_bid     = $product->get_user_max_bid( $product_id, get_current_user_id() );
$max_min_bid_text = $product->get_auction_type() == 'reverse' ? esc_html__( 'Your min bid is', 'auctions-for-woocommerce' ) : esc_html__( 'Your max bid is', 'auctions-for-woocommerce' );
?>


<?php
// Check if user can bid (this will show penalty warnings if needed)
$user_can_bid = apply_filters('auctions_for_woocommerce_user_can_bid', true, $product, get_current_user());
?>

<?php if ( ( $product->is_closed() === false ) && ( $product->is_started() === true ) ) : ?>

	<?php do_action( 'woocommerce_before_bid_form' ); ?>

	<?php if ( $user_can_bid === true ) : ?>
	<div class="bid-form-container">
		<form class="auction_form cart" method="post" enctype='multipart/form-data' data-product_id="<?php echo intval( $product_id ); ?>">

			<?php do_action( 'woocommerce_before_bid_button' ); ?>

			<input type="hidden" name="bid" value="<?php echo esc_attr( $product_id ); ?>" />
				<div class="quantity buttons_added">
					<input type="button" value="-" class="minus" />
					<input type="text" name="bid_value" data-auction-id="<?php echo intval( $product_id ); ?>"
							<?php
							if ( 'yes' !== $product->get_auction_sealed() ) {
								?>
								value="<?php echo esc_attr( number_format( $product->bid_value(), wc_get_price_decimals() == 0 ? 2 : wc_get_price_decimals() , wc_get_price_decimal_separator(), wc_get_price_thousand_separator() ) ); ?>"
								<?php if ( 'reverse' === $product->get_auction_type() ) : ?>
									max="<?php echo esc_attr( number_format( $product->bid_value(), wc_get_price_decimals(), wc_get_price_decimal_separator(), wc_get_price_thousand_separator() ) ); ?>"
								<?php else : ?>
									min="<?php echo esc_attr( number_format( $product->bid_value(), wc_get_price_decimals(), wc_get_price_decimal_separator(), wc_get_price_thousand_separator() ) ); ?>"
								<?php endif; ?>
							<?php } ?>
							step="any" size="<?php echo intval( strlen( $product->get_curent_bid() ) ) + 6; ?>" title="bid"  class="input-text qty  bid text left">
					<input type="button" value="+" class="plus" />
				</div>
			<button type="submit" class="bid_button button alt"><?php echo wp_kses_post( apply_filters( 'bid_text', esc_html__( 'Bid', 'auctions-for-woocommerce' ), $product ) ); ?></button>
			
			<input type="hidden" name="place-bid" value="<?php echo intval( $product_id ); ?>" />
			<input type="hidden" name="product_id" value="<?php echo intval( $product_id ); ?>" />
			<?php if ( is_user_logged_in() ) { ?>
				<input type="hidden" name="user_id" value="<?php echo intval( get_current_user_id() ); ?>" />
			<?php } ?>

			<?php do_action( 'woocommerce_after_bid_button' ); ?>
			
		</form>
	
		<?php do_action( 'woocommerce_after_bid_form' ); ?>
		</div>

		<div class="buy-now-content">
			<?php
				if ( !is_null($product) && method_exists ( $product, 'get_type') && $product->get_type() === 'auction') {
					wc_get_template( 'single-product/add-to-cart/auction.php' );
				}
			?>
		</div>

	<?php else : ?>
		<!-- User cannot bid due to penalty - warning is already shown by woocommerce_before_bid_form action -->
		<div class="bid-blocked-message">
			<p><em><?php _e('Bidding is currently not available for your account.', 'woocommerce'); ?></em></p>
		</div>
	<?php endif; ?>

<?php endif; ?>
