<?php
/** OTP Login Shortcode */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * OTP Login Form Shortcode
 */
function olb_otp_login_shortcode()
{
    // If user is already logged in, redirect to my account page
    if (is_user_logged_in()) {
        return '<div class="woocommerce-info">'
            . __('Zaten giriş yaptınız.', 'woocommerce')
            . ' <a href="' . esc_url(wc_get_page_permalink('myaccount')) . '">'
            . __('Hesabım', 'woocommerce')
            . '</a></div>';
    }

    // Enqueue scripts and styles
    wp_enqueue_script('olb-otp-login');
    wp_enqueue_style('olb-otp-login');

    ob_start();
    ?>
    <div class="olb-otp-login-container">
        <div class="olb-otp-login-header">
            <h2><?php _e('Merhab<PERSON>.', 'woocommerce'); ?></h2>
            <p><?php _e('Liradan\'a giriş yap veya hesap oluştur, fırsatları kaçırma!', 'woocommerce'); ?></p>
        </div>
        <div class="olb-otp-login-tabs-container">
            <div class="olb-otp-login-tabs">
                <div class="olb-otp-tab active" data-tab="login"><?php _e('Giriş Yap', 'woocommerce'); ?></div>
                <div class="olb-otp-tab" data-tab="register"><?php _e('Üye Ol', 'woocommerce'); ?></div>
            </div>
            
            <div class="olb-otp-tab-content active" id="login-tab">
                <form id="olb-phone-login-form" class="olb-otp-form">
                    <div class="form-row">
                        <label for="login_phone_number"><?php _e('Telefon Numarası', 'woocommerce'); ?></label>
                        <div class="phone-input-container">
                            <div class="country-code">
                                <svg width="32" height="24" viewBox="0 0 32 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g>
                                    <rect width="32" height="23" transform="translate(0 0.5)" fill="#F93939"/>
                                    <path id="Vector" fill-rule="evenodd" clip-rule="evenodd" d="M21.3364 13.7664L19.5658 14.7017L19.9041 12.7191L18.4717 11.313L20.4526 11.0217L21.3364 9.21851L22.2203 11.0232L24.2012 11.3146L22.7688 12.7176L23.1071 14.7002" fill="white"/>
                                    <path id="Vector_2" fill-rule="evenodd" clip-rule="evenodd" d="M14.4759 17.3667C17.4214 17.3667 19.8092 14.964 19.8092 12C19.8092 9.0361 17.4214 6.63336 14.4759 6.63336C11.5304 6.63336 9.14258 9.0361 9.14258 12C9.14258 14.964 11.5304 17.3667 14.4759 17.3667ZM15.9997 15.8334C18.1026 15.8334 19.8092 14.116 19.8092 12C19.8092 9.88403 18.1026 8.1667 15.9997 8.1667C13.8969 8.1667 12.1902 9.88403 12.1902 12C12.1902 14.116 13.8969 15.8334 15.9997 15.8334Z" fill="white"/>
                                    </g>
                                </svg>  +90
                            </div>
                            <input type="tel" id="login_phone_number" name="phone_number" placeholder="5XX XXX XX XX" required>
                        </div>
                        <div class="login-message-container">
                            <p><?php _e('Telefonunuza onay kodu gönderilecektir.', 'woocommerce'); ?></p>
                        </div>
                    </div>
                    
                    <div class="form-row otp-row" style="display: none;">
                        <label for="login_otp_code"><?php _e('Doğrulama Kodu', 'woocommerce'); ?></label>
                        <div class="otp-input-container">
                            <input type="text" class="otp-digit" maxlength="1" pattern="[0-9]" inputmode="numeric">
                            <input type="text" class="otp-digit" maxlength="1" pattern="[0-9]" inputmode="numeric">
                            <input type="text" class="otp-digit" maxlength="1" pattern="[0-9]" inputmode="numeric">
                            <input type="text" class="otp-digit" maxlength="1" pattern="[0-9]" inputmode="numeric">
                            <input type="text" class="otp-digit" maxlength="1" pattern="[0-9]" inputmode="numeric">
                            <input type="text" class="otp-digit" maxlength="1" pattern="[0-9]" inputmode="numeric">
                            <input type="hidden" id="login_otp_code" name="otp_code">
                        </div>
                        
                    </div>
                    
                    <!-- Remember Me Checkbox -->
                    <div class="form-row remember-me-row" style="display: none;">
                        <label class="checkbox-container">
                            <input type="checkbox" name="remember_me" id="login_remember_me" checked>
                            <span class="checkmark"></span>
                            <?php _e('Hesabımı açık tut', 'woocommerce'); ?>
                        </label>
                    </div>
                    
                    <!-- Development Only: Show OTP Code -->
                    <div class="dev-otp-display" style="display: none;">
                        <p><strong><?php _e('Geliştirme Modu:', 'woocommerce'); ?></strong> <span class="dev-otp-code"></span></p>
                    </div>
                    
                    <div class="form-row">
                        <button type="submit" class="olb-otp-button send-otp-btn" data-action="login"><?php _e('Devam', 'woocommerce'); ?></button>
                        <button type="button" class="olb-otp-button verify-otp-btn" data-action="login" style="display: none;"><?php _e('Giriş Yap', 'woocommerce'); ?></button>
                        <div class="resend-otp">
                            <span class="timer"></span>
                            <a href="#" class="resend-link" style="display: none;"><?php _e('Tekrar Gönder', 'woocommerce'); ?></a>
                        </div>
                    </div>
                    
                    <input type="hidden" name="action_type" value="login">
                    <?php wp_nonce_field('olb_otp_nonce', 'olb_otp_nonce'); ?>
                </form>
            </div>
            
            <div class="olb-otp-tab-content" id="register-tab">
                <form id="olb-phone-register-form" class="olb-otp-form">
                    <div class="form-row">
                        <label for="register_phone_number"><?php _e('Telefon Numarası', 'woocommerce'); ?></label>
                        <div class="phone-input-container">
                            <div class="country-code">
                            <svg width="32" height="24" viewBox="0 0 32 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g>
                                    <rect width="32" height="23" transform="translate(0 0.5)" fill="#F93939"/>
                                    <path id="Vector" fill-rule="evenodd" clip-rule="evenodd" d="M21.3364 13.7664L19.5658 14.7017L19.9041 12.7191L18.4717 11.313L20.4526 11.0217L21.3364 9.21851L22.2203 11.0232L24.2012 11.3146L22.7688 12.7176L23.1071 14.7002" fill="white"/>
                                    <path id="Vector_2" fill-rule="evenodd" clip-rule="evenodd" d="M14.4759 17.3667C17.4214 17.3667 19.8092 14.964 19.8092 12C19.8092 9.0361 17.4214 6.63336 14.4759 6.63336C11.5304 6.63336 9.14258 9.0361 9.14258 12C9.14258 14.964 11.5304 17.3667 14.4759 17.3667ZM15.9997 15.8334C18.1026 15.8334 19.8092 14.116 19.8092 12C19.8092 9.88403 18.1026 8.1667 15.9997 8.1667C13.8969 8.1667 12.1902 9.88403 12.1902 12C12.1902 14.116 13.8969 15.8334 15.9997 15.8334Z" fill="white"/>
                                    </g>
                                </svg>  +90</div>
                            <input type="tel" id="register_phone_number" name="phone_number" placeholder="5XX XXX XX XX" required>
                        </div>
                        <div class="login-message-container">
                            <p><?php _e('Telefonunuza onay kodu gönderilecektir.', 'woocommerce'); ?></p>
                        </div>
                    </div>
                    
                    <div class="form-row otp-row" style="display: none;">
                        <label for="register_otp_code"><?php _e('Doğrulama Kodu', 'woocommerce'); ?></label>
                        <div class="otp-input-container">
                            <input type="text" class="otp-digit" maxlength="1" pattern="[0-9]" inputmode="numeric">
                            <input type="text" class="otp-digit" maxlength="1" pattern="[0-9]" inputmode="numeric">
                            <input type="text" class="otp-digit" maxlength="1" pattern="[0-9]" inputmode="numeric">
                            <input type="text" class="otp-digit" maxlength="1" pattern="[0-9]" inputmode="numeric">
                            <input type="text" class="otp-digit" maxlength="1" pattern="[0-9]" inputmode="numeric">
                            <input type="text" class="otp-digit" maxlength="1" pattern="[0-9]" inputmode="numeric">
                            <input type="hidden" id="register_otp_code" name="otp_code">
                        </div>
                    </div>
                    
                    <!-- Remember Me Checkbox -->
                    <div class="form-row remember-me-row" style="display: none;">
                        <label class="checkbox-container">
                            <input type="checkbox" name="remember_me" id="register_remember_me" checked>
                            <span class="checkmark"></span>
                            <?php _e('Hesabımı açık tut', 'woocommerce'); ?>
                        </label>
                    </div>
                    
                    <!-- Development Only: Show OTP Code -->
                    <div class="dev-otp-display" style="display: none;">
                        <p><strong><?php _e('Geliştirme Modu:', 'woocommerce'); ?></strong> <span class="dev-otp-code"></span></p>
                    </div>
                    
                    <div class="form-row">
                        <button type="submit" class="olb-otp-button send-otp-btn" data-action="register"><?php _e('Devam', 'woocommerce'); ?></button>
                        <button type="button" class="olb-otp-button verify-otp-btn" data-action="register" style="display: none;"><?php _e('Üye Ol', 'woocommerce'); ?></button>
                        
                        <div class="resend-otp">
                            <span class="timer"></span>
                            <a href="#" class="resend-link" style="display: none;"><?php _e('Tekrar Gönder', 'woocommerce'); ?></a>
                        </div>
                    </div>
                    
                    <input type="hidden" name="action_type" value="register">
                    <?php wp_nonce_field('olb_otp_nonce', 'olb_otp_nonce'); ?>
                </form>
            </div>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

add_shortcode('olb_otp_login', 'olb_otp_login_shortcode');

/**
 * Process standard WooCommerce login form
 */
function olb_process_login_form()
{
    if (!isset($_POST['login']) || !isset($_POST['woocommerce-login-nonce'])) {
        return;
    }

    if (!wp_verify_nonce($_POST['woocommerce-login-nonce'], 'woocommerce-login')) {
        return;
    }

    try {
        $creds = array(
            'user_login' => trim($_POST['username']),
            'user_password' => $_POST['password'],
            'remember' => isset($_POST['rememberme']),
        );

        $user = wp_signon($creds, is_ssl());

        if (is_wp_error($user)) {
            throw new Exception($user->get_error_message());
        }

        if (!empty($_POST['redirect'])) {
            $redirect = $_POST['redirect'];
        } else {
            $redirect = wc_get_page_permalink('myaccount');
        }

        wp_redirect($redirect);
        exit;
    } catch (Exception $e) {
        wc_add_notice($e->getMessage(), 'error');
    }
}

add_action('template_redirect', 'olb_process_login_form');
