<?php
/**
 * OTP Login Functions
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Generate OTP code
 */
function olb_generate_otp($length = 6) {
    $characters = '0123456789';
    $otp = '';
    
    for ($i = 0; $i < $length; $i++) {
        $otp .= $characters[rand(0, strlen($characters) - 1)];
    }
    
    return $otp;
}

/**
 * Send OTP to phone number
 * This is a placeholder function - you'll need to integrate with an SMS API
 */
function olb_send_otp($phone_number, $otp) {
    // Store OTP in session for verification (temporary solution)
    if (!session_id()) {
        session_start();
    }
    
    $_SESSION['olb_otp'] = $otp;
    $_SESSION['olb_otp_phone'] = $phone_number;
    $_SESSION['olb_otp_time'] = time();
    
    // Log the OTP for testing (remove in production)
    error_log('OTP for ' . $phone_number . ': ' . $otp);
    
    // Return true for now - in production, return the result from the SMS API
    return true;
}

/**
 * Verify OTP
 */
function olb_verify_otp($phone_number, $otp) {
    if (!session_id()) {
        session_start();
    }
    
    // Check if OTP exists in session
    if (!isset($_SESSION['olb_otp']) || !isset($_SESSION['olb_otp_phone']) || !isset($_SESSION['olb_otp_time'])) {
        return false;
    }
    
    // Check if OTP is valid
    $stored_otp = $_SESSION['olb_otp'];
    $stored_phone = $_SESSION['olb_otp_phone'];
    $stored_time = $_SESSION['olb_otp_time'];
    
    // OTP expires after 5 minutes
    $expiry_time = 5 * 60;
    
    if ($stored_otp === $otp && $stored_phone === $phone_number && (time() - $stored_time) < $expiry_time) {
        // Clear OTP from session
        unset($_SESSION['olb_otp']);
        unset($_SESSION['olb_otp_phone']);
        unset($_SESSION['olb_otp_time']);
        
        return true;
    }
    
    return false;
}

/**
 * Login user with phone number
 */
function olb_login_with_phone($phone_number, $remember_me = false) {
    $users = get_users(array(
        'meta_key' => 'phone_number',
        'meta_value' => $phone_number,
        'number' => 1,
        'count_total' => false
    ));
    
    if (empty($users)) {
        return array(
            'success' => false,
            'message' => __('Bu telefon numarasına ait bir hesap bulunamadı.', 'woocommerce')
        );
    }
    
    $user = $users[0];
    
    // Log the user in
    wp_set_current_user($user->ID);
    wp_set_auth_cookie($user->ID, $remember_me, is_ssl());
    
    // If remember_me is true, set expiration to 48 hours
    if ($remember_me) {
        $expiration = time() + (48 * HOUR_IN_SECONDS);
        setcookie(TEST_COOKIE, 'WP Cookie check', $expiration, COOKIEPATH, COOKIE_DOMAIN);
    }
    
    return array(
        'success' => true,
        'message' => __('Giriş başarılı. Yönlendiriliyorsunuz...', 'woocommerce'),
        'redirect' => wc_get_page_permalink('myaccount')
    );
}

/**
 * Register user with phone number
 */
function olb_register_with_phone($phone_number, $remember_me = false) {
    // Check if user already exists
    $users = get_users(array(
        'meta_key' => 'phone_number',
        'meta_value' => $phone_number,
        'number' => 1,
        'count_total' => false
    ));
    
    if (!empty($users)) {
        return array(
            'success' => false,
            'message' => __('Bu telefon numarası ile kayıtlı bir hesap zaten var.', 'woocommerce')
        );
    }
    
    // Generate a random username
    $username = 'user_' . substr(md5(time() . rand()), 0, 10);
    
    // Generate a random password
    $password = wp_generate_password();
    
    // Create user
    $user_id = wp_create_user($username, $password);
    
    if (is_wp_error($user_id)) {
        return array(
            'success' => false,
            'message' => $user_id->get_error_message()
        );
    }
    
    // Add phone number as user meta
    update_user_meta($user_id, 'phone_number', $phone_number);
    
    // Set user role
    $user = new WP_User($user_id);
    $user->set_role('customer');
    
    // Log the user in
    wp_set_current_user($user_id);
    wp_set_auth_cookie($user_id, $remember_me, is_ssl());
    
    // If remember_me is true, set expiration to 48 hours
    if ($remember_me) {
        $expiration = time() + (48 * HOUR_IN_SECONDS);
        setcookie(TEST_COOKIE, 'WP Cookie check', $expiration, COOKIEPATH, COOKIE_DOMAIN);
    }
    
    return array(
        'success' => true,
        'message' => __('Kayıt başarılı. Yönlendiriliyorsunuz...', 'woocommerce'),
        'redirect' => wc_get_page_permalink('myaccount')
    );
}

/**
 * AJAX handler for sending OTP
 */
function olb_ajax_send_otp() {
    check_ajax_referer('olb_otp_nonce', 'nonce');
    
    $phone_number = isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';
    $type = isset($_POST['type']) ? sanitize_text_field($_POST['type']) : 'login';
    
    if (empty($phone_number)) {
        wp_send_json_error(['message' => __('Lütfen telefon numaranızı girin.', 'woocommerce')]);
    }
    
    // For registration, check if user already exists
    if ($type === 'register') {
        $users = get_users(array(
            'meta_key' => 'phone_number',
            'meta_value' => $phone_number,
            'number' => 1,
            'count_total' => false
        ));
        
        if (!empty($users)) {
            wp_send_json_error(['message' => __('Bu telefon numarası ile kayıtlı bir hesap zaten var.', 'woocommerce')]);
            return;
        }
    }
    
    // For login, check if user exists
    if ($type === 'login') {
        $users = get_users(array(
            'meta_key' => 'phone_number',
            'meta_value' => $phone_number,
            'number' => 1,
            'count_total' => false
        ));
        
        if (empty($users)) {
            wp_send_json_error(['message' => __('Bu telefon numarasına ait bir hesap bulunamadı.', 'woocommerce')]);
            return;
        }
    }
    
    // Generate OTP
    $otp = olb_generate_otp();
    
    // Send OTP
    $sent = olb_send_otp($phone_number, $otp);
    
    if ($sent) {
        // Always return OTP in development mode for testing
        wp_send_json_success([
            'message' => __('Doğrulama kodu gönderildi.', 'woocommerce'),
            'otp' => $otp // Always send OTP for development
        ]);
    } else {
        wp_send_json_error(['message' => __('Doğrulama kodu gönderilemedi. Lütfen tekrar deneyin.', 'woocommerce')]);
    }
}
add_action('wp_ajax_olb_send_otp', 'olb_ajax_send_otp');
add_action('wp_ajax_nopriv_olb_send_otp', 'olb_ajax_send_otp');

/**
 * AJAX handler for verifying OTP and logging in/registering
 */
function olb_ajax_verify_otp() {
    check_ajax_referer('olb_otp_nonce', 'nonce');
    
    $phone_number = isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';
    $otp = isset($_POST['otp']) ? sanitize_text_field($_POST['otp']) : '';
    $type = isset($_POST['type']) ? sanitize_text_field($_POST['type']) : 'login';
    $remember_me = isset($_POST['remember_me']) && $_POST['remember_me'] == 1;
    
    if (empty($phone_number) || empty($otp)) {
        wp_send_json_error(['message' => __('Lütfen telefon numaranızı ve doğrulama kodunu girin.', 'woocommerce')]);
    }
    
    // Verify OTP
    $verified = olb_verify_otp($phone_number, $otp);
    
    if ($verified) {
        // Login or register user based on type
        if ($type === 'login') {
            $result = olb_login_with_phone($phone_number, $remember_me);
        } else {
            $result = olb_register_with_phone($phone_number, $remember_me);
        }
        
        if ($result['success']) {
            wp_send_json_success([
                'message' => $result['message'],
                'redirect' => $result['redirect']
            ]);
        } else {
            wp_send_json_error(['message' => $result['message']]);
        }
    } else {
        wp_send_json_error(['message' => __('Doğrulama kodu geçersiz veya süresi dolmuş.', 'woocommerce')]);
    }
}
add_action('wp_ajax_olb_verify_otp', 'olb_ajax_verify_otp');
add_action('wp_ajax_nopriv_olb_verify_otp', 'olb_ajax_verify_otp'); 