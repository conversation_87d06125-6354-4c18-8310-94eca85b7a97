<?php
/**
 * Single Product Image
 *
 * Custom gallery slider with thumbnails and lightbox
 */

if (!defined('ABSPATH')) {
	exit; // Exit if accessed directly
}

global $product;

$post_thumbnail_id = $product->get_image_id();
$attachment_ids = $product->get_gallery_image_ids();

// Add featured image to the beginning of gallery
if ($post_thumbnail_id) {
	array_unshift($attachment_ids, $post_thumbnail_id);
}

// Remove duplicates
$attachment_ids = array_unique($attachment_ids);
?>

<div class="olb-product-gallery-wrapper">
	<?php if (count($attachment_ids) > 0) : ?>
		<!-- Main Slider -->
		<div class="olb-product-gallery-main">
			<?php foreach ($attachment_ids as $attachment_id) : 
				$full_size_image = wp_get_attachment_image_src($attachment_id, 'full');
				$thumbnail = wp_get_attachment_image_src($attachment_id, 'woocommerce_single');
				
				// Skip if no image is found
				if (!$full_size_image) {
					continue;
				}
				
				$attributes = array(
					'title'                   => get_post_field('post_title', $attachment_id),
					'data-caption'            => get_post_field('post_excerpt', $attachment_id),
					'data-src'                => $full_size_image[0],
					'data-large_image'        => $full_size_image[0],
					'data-large_image_width'  => $full_size_image[1],
					'data-large_image_height' => $full_size_image[2],
				);
			?>
				<div class="olb-gallery-item">
					<a href="<?php echo esc_url($full_size_image[0]); ?>" 
					   data-fancybox="product-gallery" 
					   data-caption="<?php echo esc_attr(get_post_field('post_excerpt', $attachment_id)); ?>"
					   data-type="image">
						<?php echo wp_get_attachment_image($attachment_id, 'woocommerce_single', false, $attributes); ?>
					</a>
				</div>
			<?php endforeach; ?>
		</div>
		
		<!-- Thumbnails Slider -->
		<?php if (count($attachment_ids) > 1) : ?>
			<div class="olb-product-gallery-thumbs">
				<?php foreach ($attachment_ids as $attachment_id) : 
					$thumbnail = wp_get_attachment_image_src($attachment_id, 'thumbnail');
					
					// Skip if no thumbnail is found
					if (!$thumbnail) {
						continue;
					}
				?>
					<div class="olb-gallery-thumb">
						<?php echo wp_get_attachment_image($attachment_id, 'thumbnail'); ?>
					</div>
				<?php endforeach; ?>
			</div>
		<?php endif; ?>
		
		<!-- Custom Navigation Buttons -->
		<div class="olb-gallery-nav">
			<button class="olb-gallery-prev">
				<svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
				<g clip-path="url(#clip0_211_24414)">
					<path d="M15.41 17.1691L10.83 12.5791L15.41 7.9891L14 6.5791L8 12.5791L14 18.5791L15.41 17.1691Z" fill="#F9F9FB"/>
				</g>
				<defs>
					<clipPath id="clip0_211_24414">
					<rect width="24" height="24" fill="white" transform="translate(0 0.579102)"/>
					</clipPath>
				</defs>
				</svg>
			</button>
			<button class="olb-gallery-next">
				<svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
				<g clip-path="url(#clip0_211_24416)">
					<path d="M8.59 17.1691L13.17 12.5791L8.59 7.9891L10 6.5791L16 12.5791L10 18.5791L8.59 17.1691Z" fill="#F9F9FB"/>
				</g>
				<defs>
					<clipPath id="clip0_211_24416">
					<rect width="24" height="24" fill="white" transform="matrix(-1 0 0 1 24 0.579102)"/>
					</clipPath>
				</defs>
				</svg>
			</button>
		</div>
	<?php else : ?>
		<div class="woocommerce-product-gallery__image--placeholder">
			<img src="<?php echo esc_url(wc_placeholder_img_src('woocommerce_single')); ?>" alt="<?php esc_html_e('Awaiting product image', 'woocommerce'); ?>" class="wp-post-image" />
		</div>
	<?php endif; ?>
</div>
