/* O<PERSON> Login Styles */
.olb-otp-login-container {
  max-width: 780px;
  margin: 0 auto;
  padding: 20px;
  font-family: "Manrope", sans-serif;
}

.olb-otp-login-header {
  text-align: center;
  margin-bottom: 30px;
}

.olb-otp-login-header h2 {
  color: #000;
  text-align: center;

  /* Display M/Bold */
  font-family: var(--gp-font--manrope);
  font-size: 36px;
  font-style: normal;
  font-weight: 700;
  line-height: 44px; /* 122.222% */
}

.olb-otp-login-header p {
  color: #000;
  text-align: center;

  /* Display SM/Medium */
  font-family: var(--gp-font--manrope);
  font-size: 28px;
  font-style: normal;
  font-weight: 500;
  line-height: 36px; /* 128.571% */
}
.olb-otp-login-tabs-container {
  border-radius: var(--border-radius-lg, 8px);
  border: 1px solid #e4e4e7;
  background: #fff;
  display: flex;
  max-width: 500px;
  padding: var(--spacing-6, 24px);
  flex-direction: column;
  align-items: flex-start;
  gap: var(--spacing-6, 24px);
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
  margin: 0 auto;
}
.olb-otp-login-tabs {
  display: flex;
  height: 36px;
  padding: 4px;
  align-items: center;
  align-self: stretch;
  border-radius: 20px;
  background: #fef2f0;
}

.olb-otp-tab {
  flex: 0 0 50%;
  text-align: center;
  color: var(--Theme-Brand-2-500, #fc5003);
  display: flex;
  padding: var(--spacing-1, 4px) var(--spacing-3, 12px);
  justify-content: center;
  align-items: center;
  gap: var(--spacing-2, 8px);
  flex: 1 0 0;
  align-self: stretch;
  /* Text SM/Medium */
  font-family: var(--gp-font--manrope);
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px; /* 142.857% */
  cursor: pointer;
}

.olb-otp-tab.active {
  border-radius: var(--Number, 20px);
  background: var(--Theme-Brand-2-500, #fc5003);
  color: #fff;
  /* shadow/sm */
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
}

.olb-otp-tab-content {
  display: none;
  width: 100%;
}

.olb-otp-tab-content.active {
  display: block;
}

.olb-otp-form .form-row {
  margin-bottom: 20px;
}

.olb-otp-form label {
  display: block;
  color: var(--Theme-Brand-1-500, #050632);

  /* Text M/Medium */
  font-family: var(--gp-font--manrope);
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px; /* 150% */
}

.olb-otp-form input[type="tel"],
.olb-otp-form input[type="text"],
.olb-otp-form input[type="email"],
.olb-otp-form input[type="password"] {
  width: 100%; border-radius: 8px;
  border: 1px solid var(--Black-1, #CFD3D4);
  background-color: var(--White, #FFF);
  font-size: 14px;
  box-sizing: border-box;
}

.phone-input-container {
  display: flex;
  height: 52px;
  gap: 6px;
}

.country-code {
  border-radius: 8px;
  border: 1px solid #cfd3d4;
  height: 100%;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 16px;
  color: #abafb1;
  font-family: var(--gp-font--manrope);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;  
}

.phone-input-container input {
    border-radius: 8px;
    border: 1px solid #cfd3d4;
    background-color: #fff;
    height: 100%;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0 16px;
    color: #abafb1;
    font-family: var(--gp-font--manrope);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;  
}

.phone-input-container input::placeholder {
  color: #abafb1;
}
.login-message-container p {
    color: var(--Black-2, #ABAFB1); 
    font-family: var(--gp-font--manrope);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px; 
    margin: 4px 0 16px;
}
.woocommerce form .form-row {
    padding: 0;
    margin: 0;
}
.olb-otp-button {
  width: 100%;
  padding: 12px;
  background: #050632;
  color: #fff;
  border: none;
  border-radius: 32px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.olb-otp-button:hover {
  background: #0a0a4d;
}

.resend-otp {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  margin-top: 8px;
}

.resend-link {
  color: #050632;
  text-decoration: underline;
}

.lost-password {
  display: block;
  text-align: center;
  font-size: 14px;
  color: #050632;
  text-decoration: underline;
}

/* OTP Input Styles */
.otp-row {
  position: relative;
}
.otp-row 

.timer {
  color: #666;
}

/* Message Styles */
.olb-message {
  padding: 10px 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 14px;
}

.olb-message.success {
  background-color: #e7f5ea;
  color: #0f5132;
  border: 1px solid #badbcc;
}

.olb-message.error {
  background-color: #f8d7da;
  color: #842029;
  border: 1px solid #f5c2c7;
}

/* OTP Input Container Styles */
.otp-input-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0;
} 

.otp-digit {
  width: 42px;
  height: 36px;
  text-align: center;
  font-size: 24px;
  font-weight: 500;
  border: 1px solid #e1e1e1;
  background-color: #fff;
  border-radius: 0 !important; 
  padding: 0;
  margin: 0 !important;
}
.otp-digit:focus, .otp-digit:active {
    outline: none !important;
    box-shadow: none !important;
}
.otp-input-container input:first-child { 
    border-radius: 8px 0 0 8px !important;
}
.otp-input-container input:nth-last-child(2) { 
    border-radius: 0 8px 8px 0 !important;
}


.otp-digit:focus {
  border-color: #050632;
  outline: none;
  box-shadow: 0 0 0 2px rgba(5, 6, 50, 0.2);
}

/* Development OTP Display */
.dev-otp-display {
  margin: 10px 0;
  padding: 10px;
  background-color: #f8f9fa;
  border: 1px dashed #ccc;
  border-radius: 4px;
  font-family: monospace;
}

.dev-otp-display p {
  margin: 0;
  font-size: 14px;
}

.dev-otp-code {
  font-weight: bold;
  color: #dc3545;
  letter-spacing: 3px;
}

/* Checkbox Styles */
.remember-me-row {
  margin-top: 16px !important;
  margin-bottom: 16px !important;

}

.checkbox-container {
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 30px;
  cursor: pointer;color: #18181B; 
  font-family: var(--gp-font--manrope);
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 100%; /* 14px */
}

.checkbox-container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 4px;
  left: 0;
  height: 20px;
  width: 20px;
  background-color: #fff;
  border: 1px solid #cfd3d4;
  border-radius: 4px;
}

.checkbox-container:hover input ~ .checkmark {
  background-color: #fff;
}

.checkbox-container input:checked ~ .checkmark {
  background-color: #fc5003;
  border-color: #fc5003;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.checkbox-container input:checked ~ .checkmark:after {
  display: block;
}

.checkbox-container .checkmark:after {
  left: 7px;
  top: 3px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
