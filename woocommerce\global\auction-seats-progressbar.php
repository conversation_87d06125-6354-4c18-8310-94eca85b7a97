<?php
/**
 * Progressbar template
 *
 */

if ( ! defined( 'ABSPATH' ) ) {
exit; // Exit if accessed directly
}
global  $product, $post;

if ( $product->get_type() === 'auction' && is_auction_seated( $product->get_id() ) && ( get_option( 'wc_simple_auctions_seated_progressbar' , 'yes' ) === 'yes' ) ) :
	$seat_product_id = get_post_meta( $product->get_id(), '_auction_seated_product_id', true);
	$seat_product    = wc_get_product( $seat_product_id );
	$seats_left      = get_auction_seats_left( $product->get_id() );
	$max_seats       = $seat_product->get_total_sales() + $seats_left;
	?>

<div class="afw-seats-progress-meter
<?php
	if ( get_auction_seats_left() <= 0 ) {
	echo 'full';}
	?>
">

	<span class="zero">0</span>

	<span class="sold"><?php echo  esc_html__( 'Seats available:', 'auctions-for-woocommerce' ); ?> <?php echo intval( get_auction_seats_left() ); ?></span>


	<span class="max"><?php echo intval( $max_seats ); ?></span>

	<progress  max="<?php echo intval( $max_seats ); ?>" value="<?php echo intval( get_auction_seats_left() ); ?>"  low="0"></progress>
</div>

<?php endif; ?>
