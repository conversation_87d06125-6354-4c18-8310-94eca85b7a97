import http from 'k6/http';
import { check } from 'k6';

export const options = {
  stages: [
    { duration: '15s', target: 100 },  // 0–15 sn: 100 kullanıcı
    { duration: '15s', target: 200 },  // 15–30 sn: 200 kullanıcı
    { duration: '15s', target: 300 },  // 30–45 sn: 300 kullanıcı
    { duration: '15s', target: 400 },  // 45–60 sn: 400 kullanıcı
    { duration: '15s', target: 500 },  // 60–75 sn: 500 kullanıcı
    { duration: '15s', target: 0 },    // 75–90 sn: yavaşça boşalt
  ],
  insecureSkipTLSVerify: true, // self-signed TLS için
};

export default function () {
  const res = http.get('https://localhost/auctions/');
  check(res, {
    'status is 200': (r) => r.status === 200,
    'content includes title': (r) => r.body && r.body.includes('<title'),
  });
}
