/*
Theme Name: GeneratePress Child
Theme URI: https://generatepress.com
Template: generatepress
Author: <PERSON>
Author URI: https://tomusborne.com
Description: GeneratePress is a lightweight WordPress theme built with a focus on speed and usability. Performance is important to us, which is why a fresh GeneratePress install adds less than 10kb (gzipped) to your page size. We take full advantage of the block editor (<PERSON><PERSON><PERSON>), which gives you more control over creating your content. If you use page builders, GeneratePress is the right theme for you. It is completely compatible with all major page builders, including Beaver Builder and Elementor. Thanks to our emphasis on WordPress coding standards, we can boast full compatibility with all well-coded plugins, including WooCommerce. GeneratePress is fully responsive, uses valid HTML/CSS, and is translated into over 25 languages by our amazing community of users. A few of our many features include 60+ color controls, powerful dynamic typography, 5 navigation locations, 5 sidebar layouts, dropdown menus (click or hover), and 9 widget areas. Learn more and check out our powerful premium version at https://generatepress.com
Tags: two-columns,three-columns,one-column,right-sidebar,left-sidebar,footer-widgets,blog,e-commerce,flexible-header,full-width-template,buddypress,custom-header,custom-background,custom-menu,custom-colors,sticky-post,threaded-comments,translation-ready,rtl-language-support,featured-images,theme-options
Version: 3.5.1.1746129332
Updated: 2025-05-01 22:55:32

*/

.olb-mega-menu-content {
    width: var(--gb-container-width) !important;
}

.nav-below-header .main-navigation .inside-navigation.grid-container, .nav-above-header .main-navigation .inside-navigation.grid-container {
    padding: 0;
}

.main-navigation .main-nav ul li:first-child a {
 
}

.main-navigation .main-nav ul li.all-categories a {
	background: #FFFFFF;
/* Shadows/Top Navigation Bar */
box-shadow: 0px 0px 24px rgba(34, 42, 53, 0.06), 0px 1px 1px rgba(0, 0, 0, 0.05), 0px 0px 0px 1px rgba(34, 42, 53, 0.04), 0px 0px 4px rgba(34, 42, 53, 0.08), 0px 16px 68px rgba(47, 48, 55, 0.05), inset 0px 1px 0px rgba(255, 255, 255, 0.1);
backdrop-filter: blur(5px);
/* Note: backdrop-filter has minimal browser support */
border-radius: 4px;
padding: 0 16px;
	z-index:2;
}
body .dropdown-click .main-navigation ul.toggled-on, .dropdown-click .main-navigation ul li.sfHover > ul.toggled-on {
	
    padding-top: 16px;
    background: transparent;
    box-shadow: 0;
    box-shadow: none;
}
.main-navigation .main-nav ul li.all-categories a   {
	display: flex;
	align-items: center; 
}
.main-navigation .main-nav ul li.all-categories .dropdown-menu-toggle {
	display: none;
}
.main-navigation .main-nav ul li.all-categories a svg {
	margin-right: 4px;
}
.wc_auctions_countdown-row {
    display: block !important;
}
.wc_auctions_countdown-section {
    display: inline-block !important;
}
.wc_auctions_countdown-amount,  .wc_auctions_countdown-period {
    display: inline-block !important; 
    font-family: 'Manrope';
    font-style: normal;
    font-weight: 700;
    font-size: 12px !important;
    line-height: 16px !important; 
    color: #FB3848; 
    width: auto !important;
}
.wc_auctions_countdown-amount {
    font-size: 14px;
}
.wc_auctions_countdown-section {
    width: auto !important;
    padding: 0 4px;
    margin: 0 auto;
}
.auction-time-remaining {
    display: flex;
    justify-content: center;
    font-weight: 700;
    margin : 10px auto;
    font-size: 12px !important;
    line-height: 16px !important; 
    color: #FB3848; 
}

.wp-block-search__inside-wrapper  {
    background: #FFFFFF;
    box-shadow: 0px 4px 12px rgba(13, 10, 44, 0.06);
    border-radius: 4px;
    border: 4px solid #FFFFFF;
    background: #FFFFFF;
    /* Shadow 3 */
    box-shadow: 0px 4px 12px rgba(13, 10, 44, 0.06);
    border-radius: 4px;
    overflow: hidden;   
}
.wp-block-search__button { 
    margin: 0;
    background: #050632;
    border-radius: 0 4px 4px 0;
}/* Sample */
 .wp-block-search__inside-wrapper  input {
    border: none;
    outline: none;

 }

#main-header .wp-block-search__button-outside {
    min-width: 560px;
 }

#main-header .user-activity-button {
    background-color: transparent;
    border: none;  
    color: #11263C;
    font-family: 'Manrope';
    font-style: normal;
    font-weight: 700;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap:8px;
    text-decoration: none !important;
 }

 #main-header .user-activity-button .user-activity-count-container {
   position: relative;
 }
 #main-header .user-activity-button .user-activity-count-container .user-activity-count {
    border: none;
    background-color: #434357;
    position: absolute;
    top: 0;
    right: -10%;
    width: auto;
    padding: 3px 4px;
    height: auto;
    border-radius: 50%;
    color: #fff;
    font-size: 10px;
    line-height: 1;
    display: flex ;
    align-items: center;
    justify-content: center;
 }

 

/* Kullanıcı Hesap Menüsü Stilleri */
.user-login-button-container {
    position: relative;
    display: inline-block;
}

.user-account-button { 
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 5px 8px;
    gap: 4px; 
    color: #11263C !important;
    background: #FFFFFF !important; 
    box-shadow: 0px 0px 24px rgba(34, 42, 53, 0.06), 0px 1px 1px rgba(0, 0, 0, 0.05), 0px 0px 0px 1px rgba(34, 42, 53, 0.04), 0px 0px 4px rgba(34, 42, 53, 0.08), 0px 16px 68px rgba(47, 48, 55, 0.05), inset 0px 1px 0px rgba(255, 255, 255, 0.1);
    border-radius: 4px; 
    font-family: 'Manrope';
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;  
    color: #11263C; 
    text-decoration: none !important;
 
}

.user-account-button.login-button {
    padding: 10px;
}

.user-account-button:hover {
    background-color: #f5f5f5;
}

.user-account-button svg {
    margin-left: 4px;
    transition: transform 0.3s ease;
}

.user-login-menu-container {
    position: absolute;
    top: 100%;
    right: 0;
    width: 200px;
    background-color: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    z-index: 100;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.user-login-button-container:hover .user-login-menu-container,
.user-login-button-container:focus-within .user-login-menu-container {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-login-button-container:hover .user-account-button svg,
.user-login-button-container:focus-within .user-account-button svg {
    transform: rotate(180deg);
}

.user-login-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    width: 100%;
}

.user-login-menu li {
    padding: 0;
    margin: 0;
    border-bottom: 1px solid #f0f0f0;  

}

.user-login-menu li:last-child {
    border-bottom: none;
}

.user-login-menu li a {
    display: block;
    padding: 12px 16px;
    font-family: 'Manrope';
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    line-height: 16px; 
    color: #000000; 
    display: flex;
    align-items: center;
    gap: 4px;
    text-decoration: none;
    transition: all 0.2s ease;
}

.user-login-menu li a:hover {
    background-color: #f5f5f5;
    color: #0a0a2c;
}

/* Mobil Uyumluluk */
@media (max-width: 768px) {
    .user-login-menu-container {
        width: 100%;
        left: 0;
    }
}


/* WooCommerce Loop için Açık Artırma Ürün Stilleri - Daha spesifik seçiciler */
.woocommerce ul.products li.product.product-type-auction {
    background: #FBFBFB;
    border-radius: 8px;
    padding: 24px;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    display: flex;
    flex-direction: column;
    margin: 0;
}

.woocommerce ul.products li.product.product-type-auction:hover  {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}
.woocommerce ul.products li.product.product-type-auction .auction-product-image {
  
}
.woocommerce ul.products li.product.product-type-auction .auction-product-image img { 
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
}

.woocommerce ul.products li.product .auction-product-title {
    font-family: 'Manrope';
    font-style: normal;
    font-weight: 700;
    font-size: 20px;
    line-height: 30px; 
    color: #252B42; 
    text-align: left;
    border-bottom: 1px solid #D4D4DE;
    padding-bottom: 16px;
    margin-bottom: 16px;
}

.woocommerce ul.products li.product .auction-product-title a {
    color: #333;
    text-decoration: none;
}

.woocommerce ul.products li.product .auction-current-bid,
.woocommerce ul.products li.product .auction-starting-price {
    
    display: flex;
    justify-content: space-between;
    font-family: 'Manrope', sans-serif;
    font-weight: 500;
    font-size: 14px;
    color: #666; 
}

.woocommerce ul.products li.product .auction-price,
.woocommerce ul.products li.product .auction-starting-price-value{
    font-family: 'Manrope', sans-serif;
    font-weight: 700;
    font-size: 14px;
    color: #121217;
}

.woocommerce ul.products li.product .auction-time-remaining {
    
}

.woocommerce ul.products li.product .auction-bid-button {
    margin-top: auto;
}

.woocommerce ul.products li.product .auction-bid-button .button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background-color: #0a0a2c;
    color: white;
    border-radius: 4px;
    padding: 10px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    width: 100%;
    margin-top: 0 !important;
}

.woocommerce ul.products li.product .auction-bid-button .button:hover {
    background-color: #1a1a4c;
}
 
.time-left { 
    white-space: nowrap;
}

/* Mobil uyumluluk */
@media (max-width: 768px) {
    .woocommerce ul.products li.product .auction-product {
        padding: 15px;
    }
}

.woocommerce-archive-wrapper .inside-article {
    padding: 0;
}

/* Product title truncation in loops */
.woocommerce ul.products li.product .woocommerce-loop-product__title,
.woocommerce-loop-product__title {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 2.5em; /* Adjust based on your line-height */
    line-height: 1.25em;
    margin-bottom: 0.5em;
}

/* Auction product title truncation with improved spacing */
.woocommerce ul.products li.product .auction-product-title {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden !important;
    text-overflow: ellipsis;
    max-height: 50px !important;
    line-height: 30px;
    border-bottom: 1px solid #D4D4DE;
    padding-bottom: 12px;
    margin-bottom: 12px;
    min-height: 77px;
}

/* Add padding to the bottom of the text to create space before the border */
.woocommerce ul.products li.product .auction-product-title a {
    display: inline-block;
    padding-bottom: 6px;
    color: #333;
    text-decoration: none;
}

