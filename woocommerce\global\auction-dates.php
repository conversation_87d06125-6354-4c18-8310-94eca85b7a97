<?php
/**
 * Auction dates template
 *
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

global  $product;

$gmt_offset = get_option( 'gmt_offset' ) > 0 ? '+' . get_option( 'gmt_offset' ) : get_option( 'gmt_offset' );
$dateformat = get_option( 'date_format' );
$timeformat = get_option( 'time_format' );
$product_id = $product->get_id();

if ( $product->get_type() !== 'auction' ) {
	return;
}
if ( ( $product->is_closed() === false ) && ( $product->is_started() === true ) ) : ?>
<p class="auction-end"><?php echo wp_kses_post( apply_filters( 'time_left_text', esc_html__( 'Auction ends:', 'auctions-for-woocommerce' ), $product ) ); ?> <?php echo esc_html( date_i18n( get_option( 'date_format' ), strtotime( $product->get_auction_end_time() ) ) ); ?>  <?php echo esc_html( date_i18n( get_option( 'time_format' ), strtotime( $product->get_auction_end_time() ) ) ); ?> <br />
			<?php
			printf(
				// translators: 1) timezone
				esc_html__( 'Timezone: %s', 'auctions-for-woocommerce' ),
				get_option( 'timezone_string' ) ? esc_html( get_option( 'timezone_string' ) ) : esc_html__( 'UTC', 'auctions-for-woocommerce' ) . esc_html( $gmt_offset )
			);
			?>
		</p>
	<?php
elseif ( ( $product->is_closed() === false ) && ( $product->is_started() === false ) ) :
	?>
	<p class="auction-starts"><?php echo wp_kses_post( apply_filters( 'time_text', esc_html__( 'Auction starts:', 'auctions-for-woocommerce' ), $product_id ) ); ?> <?php echo esc_html( date_i18n( get_option( 'date_format' ), strtotime( $product->get_auction_start_time() ) ) ); ?>  <?php echo esc_html( date_i18n( get_option( 'time_format' ), strtotime( $product->get_auction_start_time() ) ) ); ?></p>
	<p class="auction-end"><?php echo wp_kses_post( apply_filters( 'time_text', esc_html__( 'Auction ends:', 'auctions-for-woocommerce' ), $product_id ) ); ?> <?php echo esc_html( date_i18n( get_option( 'date_format' ), strtotime( $product->get_auction_end_time() ) ) ); ?>  <?php echo esc_html( date_i18n( get_option( 'time_format' ), strtotime( $product->get_auction_end_time() ) ) ); ?> </p>
	<?php
endif;
