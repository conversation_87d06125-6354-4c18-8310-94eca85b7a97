<?php
/**
 * The template for displaying product content within loops
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/content-product.php.
 */

defined('ABSPATH') || exit;

global $product;

// Ensure visibility
if (empty($product) || !$product->is_visible()) {
    return;
}

// Açık artırma ürünü mü kontrol et
$is_auction = $product->is_type('auction');
?>
<li <?php wc_product_class('', $product); ?>>
    <?php if ($is_auction): ?>
        <div class="auction-product">
            <div class="auction-product-image">
                <a href="<?php echo esc_url(get_permalink()); ?>">
                    <?php echo woocommerce_get_product_thumbnail(); ?>
                </a>
            </div>
            
            <h3 class="auction-product-title">
                <a href="<?php echo esc_url(get_permalink()); ?>"><?php the_title(); ?></a>
            </h3>
            
            <?php if (method_exists($product, 'get_curent_bid')): ?>
            <div class="auction-current-bid">
                <span class="auction-price-label">Mevcut teklif fiyatı: </span>
                <span class="auction-price"><?php echo wc_price($product->get_curent_bid()); ?></span>
            </div>
            <?php endif; ?>
            
            <div class="auction-starting-price">
                <span class="auction-starting-price-label">Açık artırma fiyatı: </span>
                <span class="auction-starting-price-value">1₺</span>
            </div>
            
            <div class="auction-time-remaining"> 
                <?php wc_get_template('global/auction-counter.php'); ?>
            </div>
            
            <div class="auction-bid-button">
                <a href="<?php echo esc_url(get_permalink()); ?>" class="button">Teklif Ver <svg width="9" height="10" viewBox="0 0 9 10" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M1.16699 1.66675H7.83366M7.83366 1.66675V8.33341M7.83366 1.66675L1.16699 8.33341" stroke="#F9F9FB" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</a>
            </div>
        </div>
    <?php else: ?>
        <?php
        /**
         * Hook: woocommerce_before_shop_loop_item.
         */
        do_action('woocommerce_before_shop_loop_item');

        /**
         * Hook: woocommerce_before_shop_loop_item_title.
         */
        do_action('woocommerce_before_shop_loop_item_title');

        /**
         * Hook: woocommerce_shop_loop_item_title.
         */
        do_action('woocommerce_shop_loop_item_title');

        /**
         * Hook: woocommerce_after_shop_loop_item_title.
         */
        do_action('woocommerce_after_shop_loop_item_title');

        /**
         * Hook: woocommerce_after_shop_loop_item.
         */
        do_action('woocommerce_after_shop_loop_item');
        ?>
        <h2 class="woocommerce-loop-product__title" title="<?php echo esc_attr(get_the_title()); ?>">
            <?php the_title(); ?>
        </h2>
    <?php endif; ?>
</li> 