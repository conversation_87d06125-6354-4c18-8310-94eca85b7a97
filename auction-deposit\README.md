# Auction Penalty System

A comprehensive penalty system for WooCommerce auctions that tracks auction winners and applies penalties for users who fail to purchase within 24 hours.

## Features

### Core Functionality
- **Winner Tracking**: Automatically tracks auction winners when auctions finish
- **24-Hour Purchase Window**: Monitors if winners complete their purchase within 24 hours
- **Penalty System**: Applies progressive penalties for non-compliance
- **Winner Reassignment**: Automatically assigns auction to second highest bidder if winner fails to purchase
- **Admin Interface**: Complete admin panel for managing penalties and monitoring system

### Penalty Levels
- **Level 0**: No penalty - Users can bid freely
- **Level 1**: 10% deposit required - **BIDDING BLOCKED** until deposit paid
- **Level 2**: 40% deposit required - **BIDDING BLOCKED** until deposit paid
- **Level 3**: Bidding completely blocked permanently

### Database Tables
The system creates two custom tables:
- `wp_auction_winner_tracking`: Tracks auction winners and their purchase status
- `wp_auction_penalty_log`: Logs all penalty changes and reasons

## Installation

1. The system is automatically included when you add the include statement to `functions.php`
2. Database tables are created automatically when first accessed
3. Set up cron jobs for automated processing (see Cron Jobs section)

## File Structure

```
auction-deposit/
├── auction-penalty-system.php      # Main system coordinator
├── auction-cron-jobs.php          # Cron job handlers
├── auction-winner-tracking.php    # Winner tracking functionality
├── auction-deposit-requirements.php # Deposit logic and warnings
├── auction-admin-interface.php    # Admin panel
└── README.md                      # This documentation
```

## Cron Jobs

For the system to work properly, set up these cron jobs:

### Required Cron Jobs
```bash
# Check winners every minute
* * * * * curl --silent https://yoursite.com/?auction-penalty-cron=check-winners

# Process penalties every hour
0 * * * * curl --silent https://yoursite.com/?auction-penalty-cron=process-penalties
```

### Alternative: WordPress Cron
The system also registers WordPress cron events that will run automatically:
- `auction_check_winners_cron` - Every minute
- `auction_process_penalties_cron` - Every hour

## Admin Interface

Access the admin interface at: **WooCommerce > Auction Penalty**

### Admin Tabs

#### 1. Overview
- System statistics
- Penalty level distribution
- Winner tracking success rates
- Cron job setup instructions

#### 2. User Penalties
- List of all users with penalties
- Update penalty levels manually
- Reset penalties
- View penalty descriptions

#### 3. Winner Tracking
- Current pending winners
- Purchase deadlines
- Expired purchase windows
- Winner status tracking

#### 4. Deposit History
- Users currently requiring deposits
- Penalty history and deposit events
- Deposit impact tracking
- Future deposit payment records

#### 5. Settings
- System configuration information
- Penalty level descriptions
- Purchase window settings
- Test penalty system functionality

## How It Works

### 1. Auction Finishes
When an auction finishes, the system:
- Detects the auction winner
- Creates a tracking record
- Sets 24-hour purchase deadline
- Sends notification email to winner

### 2. Purchase Window Monitoring
The cron job checks every minute for:
- Expired purchase windows
- Completed purchases
- Users who need penalties

### 3. Penalty Application
If a user doesn't purchase within 24 hours:
- User penalty level increases by 1
- Winner is removed from auction
- Second highest bidder becomes new winner
- New 24-hour window starts for new winner

### 4. Bidding Restrictions
When users try to bid:
- System checks their penalty level
- Displays appropriate warnings
- Blocks bidding for level 3 users
- Shows deposit requirements for levels 1-2

## Integration with Auctions Plugin

The system hooks into these auction plugin events:
- `auctions_for_woocommerce_finished`
- `auctions_for_woocommerce_won`
- `auctions_for_woocommerce_user_can_bid`

## User Experience

### For Users with No Penalty (Level 0)
- Can bid normally without restrictions
- No additional requirements

### For Users with Level 1 Penalty
- See warning: "Deposit Required - Bidding Blocked"
- **CANNOT BID** until 10% deposit is paid
- Warning explains the reason for penalty
- Instructions on how to enable bidding

### For Users with Level 2 Penalty
- See warning: "Higher Deposit Required - Bidding Blocked"
- **CANNOT BID** until 40% deposit is paid
- Warning explains multiple failures
- Instructions on how to enable bidding

### For Users with Level 3 Penalty
- Cannot bid at all
- See "Bidding Blocked" message
- Directed to contact customer service

## Future Enhancements

### Phase 2: Deposit Payment System
- Actual payment processing for deposits
- Integration with payment gateways
- Deposit refund on auction win
- Deposit forfeiture on auction loss

### Phase 3: Advanced Features
- Penalty reduction over time
- Different penalty rules per product category
- Bulk penalty management
- Advanced reporting and analytics

## Technical Details

### Database Schema

#### auction_winner_tracking
```sql
CREATE TABLE wp_auction_winner_tracking (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    product_id bigint(20) NOT NULL,
    winner_id bigint(20) NOT NULL,
    auction_end_time datetime NOT NULL,
    purchase_deadline datetime NOT NULL,
    status varchar(20) DEFAULT 'pending',
    date_created datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY product_id (product_id),
    KEY winner_id (winner_id),
    KEY status (status)
);
```

#### auction_penalty_log
```sql
CREATE TABLE wp_auction_penalty_log (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    user_id bigint(20) NOT NULL,
    penalty_level tinyint(1) NOT NULL,
    reason text NOT NULL,
    date_created datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY user_id (user_id)
);
```

### User Meta Fields
- `user_penalty`: Stores user's current penalty level (0-3)

## Troubleshooting

### Common Issues

1. **Cron jobs not running**
   - Verify cron job setup with hosting provider
   - Check if WordPress cron is working: `wp cron event list`
   - Test manual cron execution

2. **Penalties not applying**
   - Check if auction plugin hooks are firing
   - Verify database table creation
   - Review error logs

3. **Admin interface not showing**
   - Ensure user has `manage_woocommerce` capability
   - Check for plugin conflicts
   - Verify file permissions

### Debug Mode
Enable WordPress debug mode to see detailed logs:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Support

For issues or questions:
1. Check the error logs first
2. Verify cron job setup
3. Test with a simple auction
4. Review admin interface for system status

## Version History

### v1.0.0
- Initial implementation
- Basic penalty system (levels 0-3)
- Winner tracking and reassignment
- Admin interface
- Cron job integration
- Warning messages for users

### Planned Updates
- v1.1.0: Deposit payment integration
- v1.2.0: Advanced reporting
- v1.3.0: Penalty reduction features
