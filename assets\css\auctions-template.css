/* Auction Grid */
.auction-grid {
    display: grid;
    grid-template-columns: repeat(var(--columns, 5), 1fr);
    gap: 20px;
    margin: 30px 0;
}

/* Slider Container */
.auction-slider-container {
    margin: 30px 0;
    position: relative;
    width: 100%;
}

/* <PERSON><PERSON><PERSON><PERSON> kartı */
.auction-product {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    display: flex;
    flex-direction: column;
    margin: 5px;
}

.auction-product:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* <PERSON><PERSON><PERSON><PERSON> */
.auction-product-image {
    margin-bottom: 8px; 
    width: 100%;
    object-fit: cover;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.auction-product-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
}

/* <PERSON><PERSON><PERSON><PERSON> ba<PERSON>ı */
.auction-product-title { 
    font-family: 'Manrope';
    font-style: normal;
    font-weight: 700;
    font-size: 20px;
    line-height: 30px; 
    color: #252B42; 
    text-align: left;
    border-bottom: 1px solid #D4D4DE;
    padding-bottom: 16px;
    margin-bottom: 16px;
    min-height: 32px;
    display: -webkit-box;
    -webkit-line-clamp: 2; /* Tam olarak 2 satır */
    -webkit-box-orient: vertical;
    overflow: hidden !important;
    text-overflow: ellipsis;
    max-height: 68px !important; /* 2 satır için kesin yükseklik */
}

.auction-product-title a {
    display: inline-block;
    padding-bottom: 8px;
    color: #333;
    text-decoration: none;
}

/* Fiyat bilgileri */
.auction-current-bid,
.auction-starting-price {
    display: flex;
    justify-content: space-between;
}

.auction-price-label,
.auction-starting-price-label {
    font-family: 'Manrope', sans-serif;
    font-weight: 500;
    font-size: 14px;
    color: #666;
    display: block;
}

.auction-price,
.auction-starting-price-value {
    font-family: 'Manrope', sans-serif;
    font-weight: 700;
    font-size: 16px;
    color: #121217;
}

/* Kalan süre */
.auction-time-remaining {
    margin: 10px 0;
    font-family: 'Manrope', sans-serif;
}

.auction-time-label {
    font-weight: 500;
    font-size: 14px;
    color: #666;
}

.auction-time-countdown {
    font-weight: 700;
    color: #e63946;   white-space: nowrap;
}

/* Teklif butonu */
.auction-bid-button {
    margin-top: auto;
    padding-top: 8px;
}

.auction-bid-button .button {
    background-color: #050632;
    color: white;
    padding: 8px 30px;
    border-radius: 32px;
    display: inline-flex;
    text-decoration: none;
    transition: background-color 0.3s ease;
    font-family: 'Manrope', sans-serif;
    font-weight: 700;
    font-size: 14px;
    border: none;
    cursor: pointer;
    width: 100%;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.auction-bid-button .button:hover {
    background-color: #333;
}

/* Özel Navigasyon Okları */
.custom-nav {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    pointer-events: none;
    z-index: 1;
}

.custom-prev, 
.custom-next {
    width: 40px;
    height: 40px;
    background-color: #0a0a2c; /* Koyu lacivert arka plan */
    border-radius: 50%;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white; /* Beyaz ok rengi */
    font-size: 18px;
    margin: 0 -20px;
    pointer-events: auto;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}
.custom-nav i {
    line-height: 1;
}

.custom-prev:hover, 
.custom-next:hover {
    background-color: #1a1a4c; /* Hover durumunda biraz daha açık ton */
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* SVG ok ikonları */
.custom-prev i::before,
.custom-next i::before {
    content: "";
    display: inline-block;
    width: 24px;
    height: 24px;
    background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cg clip-path='url(%23clip0_201_59860)'%3E%3Cpath d='M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z' fill='%23F9F9FB'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_201_59860'%3E%3Crect width='24' height='24' fill='white' transform='matrix(-1 0 0 1 24 0)'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
}

/* Sol ok için SVG'yi döndür */
.custom-prev i::before {
    transform: rotate(180deg);
}

/* Responsive */
@media (max-width: 1200px) {
    .auction-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 992px) {
    .auction-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .auction-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .auction-product {
        padding: 15px;
    }
    
    .custom-nav {
        display: none;
    }
}

@media (max-width: 480px) {
    .auction-grid {
        grid-template-columns: 1fr;
    }
}
