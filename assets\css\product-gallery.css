/* Product Gallery Styles */
.olb-product-gallery-wrapper {
    position: relative;
    margin-bottom: 30px;
    width: 100%;
}

/* Main Gallery */
.olb-product-gallery-main {
    margin-bottom: 15px;
    border-radius: 8px;
    overflow: hidden;
}

.olb-product-gallery-main .olb-gallery-item {
    position: relative;
    overflow: hidden;    border: 1px solid #f2f2f2;
}

.olb-product-gallery-main .olb-gallery-item img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.3s ease;
}

.olb-product-gallery-main .olb-gallery-item:hover img {
    transform: scale(1.03);
}

/* Thumbnails */
.olb-product-gallery-thumbs {
    margin: 0 -15px;
}

.olb-product-gallery-thumbs .olb-gallery-thumb {
    padding: 0 15px;
    cursor: pointer;
    opacity: 0.6;
    transition: opacity 0.3s ease;
    border-radius: 4px;
    overflow: hidden;
}

.olb-product-gallery-thumbs .olb-gallery-thumb.slick-current {
    opacity: 1;
}

.olb-product-gallery-thumbs .olb-gallery-thumb img {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 4px;
}

.olb-product-gallery-thumbs .slick-current .olb-gallery-thumb {
    opacity: 1;
    border: 2px solid #050632;
}

/* Navigation Buttons */
.olb-gallery-nav {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    pointer-events: none;
    z-index: 10;
    margin-top: -30px;
}

.olb-gallery-prev,
.olb-gallery-next {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #050632;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    pointer-events: auto;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: visible !important;
}

.olb-gallery-prev:hover,
.olb-gallery-next:hover {
    background-color: rgba(5, 6, 50, 0.8);
}

.olb-gallery-prev svg,
.olb-gallery-next svg {
    width: 24px;
    height: 24px;
    display: block;
    overflow: visible !important;
}

.olb-gallery-prev {
    margin-left: -16px;
}

.olb-gallery-next {
    margin-right: -16px;
}

/* Custom layout for product page - 30/70 split */
.single-product div.product {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
}

.single-product div.product .olb-product-gallery-wrapper {
    width: 35%;
    flex: 0 0 35%;
}

.single-product div.product .summary.entry-summary {
    width: calc(65% - 30px);
    flex: 0 0 calc(65% - 30px);
    margin-top: 0;
    padding-left: 36px;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .single-product div.product {
        flex-direction: column;
    }
    
    .single-product div.product .olb-product-gallery-wrapper,
    .single-product div.product .summary.entry-summary {
        width: 100%;
        flex: 0 0 100%;
    }
}

@media (max-width: 768px) {
    .olb-gallery-nav {
        display: none;
    }
    
    .olb-product-gallery-thumbs .olb-gallery-thumb {
        padding: 3px;
    }
}

/* Fancybox customizations */
.fancybox__container {
    --fancybox-bg: rgba(5, 6, 50, 0.95);
}

.fancybox__toolbar {
    --fancybox-accent-color: #fff;
}

/* Hide the sale badge */
.single-product span.onsale {
    display: none !important;
}

/* Simple lightbox fallback */
.simple-lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(5, 6, 50, 0.95);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.simple-lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.simple-lightbox-content img {
    max-width: 100%;
    max-height: 90vh;
    display: block;
    margin: 0 auto;
}

.close-lightbox {
    position: absolute;
    top: -40px;
    right: 0;
    background: none;
    border: none;
    color: white;
    font-size: 30px;
    cursor: pointer;
} 

@media (max-width: 768px) {
    .single-product div.product .summary.entry-summary {
        padding-left: 0;
    }
    .olb-product-gallery-thumbs {
        margin: 0;
    }
    .olb-product-gallery-thumbs .olb-gallery-thumb img {
        padding: 12px;
        border: 1px solid #f2f2f2;
    }
}
