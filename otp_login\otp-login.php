<?php
/**
 * OTP Login Main File
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Include required files
require_once 'otp-functions.php';
require_once 'otp-shortcode.php';

/**
 * Initialize OTP Login
 */
function olb_otp_login_init() {
    // Register scripts and styles
    wp_register_script(
        'olb-otp-login',
        get_stylesheet_directory_uri() . '/otp_login/otp-login.js',
        array('jquery'),
        filemtime(get_stylesheet_directory() . '/otp_login/otp-login.js'),
        true
    );
    
    wp_register_style(
        'olb-otp-login',
        get_stylesheet_directory_uri() . '/otp_login/otp-login.css',
        array(),
        filemtime(get_stylesheet_directory() . '/otp_login/otp-login.css')
    );
    
    // Localize script with AJAX URL
    wp_localize_script('olb-otp-login', 'olb_otp_params', array(
        'ajax_url' => admin_url('admin-ajax.php')
    ));
}
add_action('init', 'olb_otp_login_init');

/**
 * Replace WooCommerce login form with OTP login
 */
function olb_replace_woocommerce_login_form() {
    // Check if we're on the my-account page and not logged in
    if (is_account_page() && !is_user_logged_in()) {
        // Remove the default WooCommerce login form
        remove_action('woocommerce_before_customer_login_form', 'woocommerce_output_all_notices', 10);
        remove_action('woocommerce_login_form_start', 'woocommerce_output_all_notices', 10);
        
        // Add our custom login form
        add_action('woocommerce_before_customer_login_form', 'olb_output_custom_login_form', 10);
    }
}
add_action('template_redirect', 'olb_replace_woocommerce_login_form');

/**
 * Output custom login form
 */
function olb_output_custom_login_form() {
    echo do_shortcode('[olb_otp_login]');
    
    // Hide the default WooCommerce login form
    ?>
    <style>
        .woocommerce-form-login, 
        .woocommerce-form-register,
        .u-column1,
        .u-column2,
        .woocommerce h2 {
            display: none !important;
        }
    </style>
    <?php
} 