<?php
/**
 * Auction Deposit Requirements
 * 
 * Handles deposit requirements based on user penalty levels:
 * - Level 0: No deposit required
 * - Level 1: 10% deposit required
 * - Level 2: 40% deposit required  
 * - Level 3: Cannot bid (blocked)
 * 
 * @package AuctionPenaltySystem
 * @version 1.0.0
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Auction Deposit Requirements Class
 */
class AuctionDepositRequirements {
    
    /**
     * Constructor
     */
    public function __construct() {
        // No hooks needed - this class is used as a utility by the main system
        // All hooks are handled by AuctionPenaltySystem class to avoid conflicts
    }
    
    /**
     * Check if user can bid based on penalty level
     */
    public function can_user_bid($user_id, $product_id = null) {
        $penalty_level = AuctionPenaltySystem::get_user_penalty_level($user_id);

        // Level 3: Cannot bid
        if ($penalty_level >= 3) {
            return false;
        }

        // Level 0: Can bid freely
        if ($penalty_level == 0) {
            return true;
        }

        // Level 1 & 2: Check if deposit has been paid for this specific product
        if ($penalty_level == 1 || $penalty_level == 2) {
            return $this->has_user_paid_deposit($user_id, $product_id);
        }

        return true;
    }
    

    

    
    /**
     * Display penalty warning based on user level
     */
    public function display_penalty_warning($user_id) {
        $penalty_level = AuctionPenaltySystem::get_user_penalty_level($user_id);

        if ($penalty_level == 0) {
            return;
        }

        global $product;
        $product_price = $product ? $product->get_regular_price() : 0;
        
        echo '<div class="auction-penalty-warning">';
        
        switch ($penalty_level) {
            case 1:
                $deposit_amount = $product_price * 0.10;
                echo '<div class="penalty-warning penalty-level-1">';
                echo '<h4>' . __('Deposit Required - Bidding Blocked', 'woocommerce') . '</h4>';
                echo '<p>' . sprintf(
                    __('You cannot bid on this auction until you pay a 10%% deposit (%s).', 'woocommerce'),
                    wc_price($deposit_amount)
                ) . '</p>';
                echo '<p class="penalty-reason">' . __('Reason: Failed to purchase a won auction within 24 hours.', 'woocommerce') . '</p>';
                echo '<p><strong>' . __('Bidding is currently blocked until deposit payment is made.', 'woocommerce') . '</strong></p>';
                $this->display_deposit_payment_button($deposit_amount, $product->get_id());
                echo '</div>';
                break;
                
            case 2:
                $deposit_amount = $product_price * 0.40;
                echo '<div class="penalty-warning penalty-level-2">';
                echo '<h4>' . __('Higher Deposit Required - Bidding Blocked', 'woocommerce') . '</h4>';
                echo '<p>' . sprintf(
                    __('You cannot bid on this auction until you pay a 40%% deposit (%s).', 'woocommerce'),
                    wc_price($deposit_amount)
                ) . '</p>';
                echo '<p class="penalty-reason">' . __('Reason: Multiple failures to purchase won auctions within 24 hours.', 'woocommerce') . '</p>';
                echo '<p><strong>' . __('Bidding is currently blocked until deposit payment is made.', 'woocommerce') . '</strong></p>';
                $this->display_deposit_payment_button($deposit_amount, $product->get_id());
                echo '</div>';
                break;
                
            case 3:
                echo '<div class="penalty-warning penalty-level-3">';
                echo '<h4>' . __('Bidding Blocked', 'woocommerce') . '</h4>';
                echo '<p>' . __('Your bidding privileges have been suspended due to repeated failures to complete purchases.', 'woocommerce') . '</p>';
                echo '<p class="penalty-reason">' . __('Please contact customer service to resolve this issue.', 'woocommerce') . '</p>';
                echo '</div>';
                break;
        }
        
        echo '</div>';
        
        // Add CSS for styling
        $this->add_penalty_warning_styles();
    }
    
    /**
     * Display deposit payment button with real payment link
     */
    private function display_deposit_payment_button($amount, $product_id) {
        $user_id = get_current_user_id();
        $penalty_level = AuctionPenaltySystem::get_user_penalty_level($user_id);

        // Get deposit product slug based on penalty level
        $deposit_slug = $penalty_level == 1 ? 'seviye-1-depozito' : 'seviye-2-depozito';

        // Create deposit payment URL with parameters
        $deposit_url = $this->create_deposit_payment_url($deposit_slug, $product_id, $amount);

        echo '<div class="deposit-payment-section">';
        echo '<div class="deposit-payment-info">';
        echo '<h5>' . __('How to Enable Bidding:', 'woocommerce') . '</h5>';
        echo '<ol>';
        echo '<li>' . sprintf(__('Pay the required deposit of %s', 'woocommerce'), wc_price($amount)) . '</li>';
        echo '<li>' . __('Once payment is confirmed, you can bid normally', 'woocommerce') . '</li>';
        echo '<li>' . __('If you win the auction, deposit will be deducted from final price', 'woocommerce') . '</li>';
        echo '</ol>';
        echo '</div>';

        if ($deposit_url) {
            echo '<a href="' . esc_url($deposit_url) . '" class="button deposit-payment-btn">';
            echo sprintf(__('Pay Deposit (%s)', 'woocommerce'), wc_price($amount));
            echo '</a>';
        } else {
            echo '<p class="error">' . __('Deposit product not found. Please contact administrator.', 'woocommerce') . '</p>';
        }

        echo '</div>';
    }

    /**
     * Create deposit payment URL with custom pricing
     */
    private function create_deposit_payment_url($deposit_slug, $auction_product_id, $amount) {
        // Get deposit product by slug
        $deposit_product = get_page_by_path($deposit_slug, OBJECT, 'product');

        if (!$deposit_product) {
            return false;
        }

        // Create URL with parameters for custom pricing and auction product tracking
        $deposit_url = get_permalink($deposit_product->ID);
        $deposit_url = add_query_arg(array(
            'auction_product_id' => $auction_product_id,
            'deposit_amount' => $amount,
            'user_id' => get_current_user_id()
        ), $deposit_url);

        return $deposit_url;
    }

    /**
     * Calculate required deposit amount
     */
    public function calculate_deposit_amount($user_id, $product_price) {
        $penalty_level = AuctionPenaltySystem::get_user_penalty_level($user_id);
        
        switch ($penalty_level) {
            case 1:
                return $product_price * 0.10; // 10%
            case 2:
                return $product_price * 0.40; // 40%
            case 3:
            default:
                return 0; // No deposit for level 0 or blocked users
        }
    }
    
    /**
     * Get deposit percentage for penalty level
     */
    public function get_deposit_percentage($penalty_level) {
        switch (intval($penalty_level)) {
            case 1:
                return 10;
            case 2:
                return 40;
            case 3:
            default:
                return 0;
        }
    }
    
    /**
     * Check if user has paid deposit for specific product
     */
    public function has_user_paid_deposit($user_id, $product_id) {
        if (!$product_id || !$user_id) {
            return false;
        }

        global $wpdb;

        // Check if user has a completed deposit payment for this specific product
        $deposit_paid = $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(*)
            FROM {$wpdb->prefix}auction_deposits
            WHERE user_id = %d
            AND auction_product_id = %d
            AND status = 'completed'
        ", $user_id, $product_id));

        return $deposit_paid > 0;
    }
    
    /**
     * Get user's penalty history
     */
    public function get_user_penalty_history($user_id, $limit = 10) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'auction_penalty_log';
        
        return $wpdb->get_results($wpdb->prepare("
            SELECT * FROM $table_name 
            WHERE user_id = %d 
            ORDER BY date_created DESC 
            LIMIT %d
        ", $user_id, $limit));
    }
    
    /**
     * Add CSS styles for penalty warnings
     */
    private function add_penalty_warning_styles() {
        static $styles_added = false;
        
        if ($styles_added) {
            return;
        }
        
        $styles_added = true;
        
        ?>
        <style>
        .auction-penalty-warning {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid;
        }
        
        .penalty-level-1 {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        .penalty-level-2 {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .penalty-level-3 {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        
        .penalty-warning h4 {
            margin: 0 0 10px 0;
            font-weight: bold;
        }
        
        .penalty-warning p {
            margin: 5px 0;
        }
        
        .penalty-reason {
            font-style: italic;
            font-size: 0.9em;
        }
        
        .deposit-payment-section {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid rgba(0,0,0,0.1);
        }

        .deposit-payment-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .deposit-payment-info h5 {
            margin: 0 0 10px 0;
            color: #495057;
        }

        .deposit-payment-info ol {
            margin: 0;
            padding-left: 20px;
        }

        .deposit-payment-info li {
            margin-bottom: 5px;
        }
        
        .deposit-payment-btn {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        
        .deposit-payment-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .deposit-payment-btn:hover:not(:disabled) {
            background-color: #005a87;
        }
        </style>
        <?php
    }
    
    /**
     * Get penalty statistics for admin
     */
    public function get_penalty_statistics() {
        global $wpdb;
        
        $stats = array();
        
        // Count users by penalty level
        for ($level = 0; $level <= 3; $level++) {
            $stats["level_{$level}"] = $wpdb->get_var($wpdb->prepare("
                SELECT COUNT(DISTINCT user_id) 
                FROM {$wpdb->usermeta} 
                WHERE meta_key = 'user_penalty' 
                AND meta_value = %d
            ", $level));
        }
        
        // Total users with penalties
        $stats['total_with_penalties'] = $wpdb->get_var("
            SELECT COUNT(DISTINCT user_id) 
            FROM {$wpdb->usermeta} 
            WHERE meta_key = 'user_penalty' 
            AND meta_value > 0
        ");
        
        // Recent penalty applications (last 30 days)
        $penalty_table = $wpdb->prefix . 'auction_penalty_log';
        $stats['recent_penalties'] = $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(*) 
            FROM $penalty_table 
            WHERE date_created > %s
        ", date('Y-m-d H:i:s', strtotime('-30 days'))));
        
        return $stats;
    }
}
