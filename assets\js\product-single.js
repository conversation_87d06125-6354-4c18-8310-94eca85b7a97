// jquery init
(function($) {
    $(document).ready(function() {
        // auctions-history-link click event
        $('#auctions-history-link').on('click', function(e) {
            e.preventDefault();
            $('#auction-history-popup').fadeIn();
        });
        
        // Close popup when clicking on close button
        $('.close-popup').on('click', function() {
            $('#auction-history-popup').fadeOut();
        });
        
        // Close popup when clicking outside of it
        $(window).on('click', function(e) {
            if ($(e.target).is('#auction-history-popup')) {
                $('#auction-history-popup').fadeOut();
               
            }
        });
        // Close popup when clicking outside of it
        $(window).on('click', function(e) {
           
            if ($(e.target).is('.buy-now-popup-content') && $(e.target).not('.buy-now-popup-inner')) {
                $('.buy-now-popup').fadeOut();
            }
        });
        // buy now popup
        $('.buy-now-popup-content .close-popup').on('click', function() {
            $('.buy-now-popup').fadeOut();
        });
        $('#buy-now-info').on('click', function() { 
            $('.buy-now-popup').fadeIn();
        });

        // product status popup
        $('.product-status-popup-content .close-popup').on('click', function() {
            $('.product-status-popup').fadeOut();
        });
        $(window).on('click', function(e) {
            if ($(e.target).is('.product-status-popup-content') && $(e.target).not('.product-status-popup-inner')) {
                $('.product-status-popup').fadeOut();
            }
        });
        $('#product-status-info').on('click', function() {
            console.log('clicked');
            $('.product-status-popup').fadeIn();
        });
    });
})(jQuery);

