<?php
/**
 * Auction max-bid template
 *
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

global  $product;


if ( $product->get_type() !== 'auction' && $product->get_auction_proxy() !== 'yes' ) {
	return;
}
$product_id       = $product->get_auction_id();
$user_max_bid     = $product->get_user_max_bid( $product_id, get_current_user_id() );
$max_min_bid_text = $product->get_auction_type() === 'reverse' ? esc_html__( 'Your min bid is', 'auctions-for-woocommerce' ) : esc_html__( 'Your max bid is', 'auctions-for-woocommerce' );

if ( 'yes' !== $product->get_auction_sealed() ) { ?>
			<?php if ( $product->get_auction_proxy() && $product->get_auction_max_current_bider() && get_current_user_id() === $product->get_auction_max_current_bider() ) { ?>
				<p class="max-bid"><?php echo esc_html( $max_min_bid_text ); ?> <?php echo wp_kses_post( wc_price( $product->get_auction_max_bid() ) ); ?>
			<?php } ?>
		<?php } elseif ( $user_max_bid > 0 ) { ?>
			<p class="max-bid"><?php echo esc_html( $max_min_bid_text ); ?> <?php echo wp_kses_post( wc_price( $user_max_bid ) ); ?>
<?php
		}

