/**
 * Admin Styles for Auction Penalty System
 */

/* Overview Tab Styles */
.auction-penalty-overview .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.auction-penalty-overview .stat-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.auction-penalty-overview .stat-card h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #23282d;
    font-size: 16px;
}

.auction-penalty-overview .stat-card ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.auction-penalty-overview .stat-card li {
    padding: 5px 0;
    border-bottom: 1px solid #f1f1f1;
}

.auction-penalty-overview .stat-card li:last-child {
    border-bottom: none;
}

.auction-penalty-overview .cron-info {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    margin-top: 20px;
}

.auction-penalty-overview .cron-info h3 {
    margin-top: 0;
    color: #23282d;
}

.auction-penalty-overview .cron-info code {
    display: block;
    background: #2c3e50;
    color: #ecf0f1;
    padding: 10px;
    margin: 10px 0;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    overflow-x: auto;
}

/* Users Tab Styles */
.auction-penalty-users .penalty-level {
    padding: 3px 8px;
    border-radius: 3px;
    color: white;
    font-weight: bold;
    font-size: 12px;
}

.auction-penalty-users .penalty-level-1 {
    background-color: #ffc107;
    color: #000;
}

.auction-penalty-users .penalty-level-2 {
    background-color: #dc3545;
}

.auction-penalty-users .penalty-level-3 {
    background-color: #17a2b8;
}

/* Modal Styles */
#update-penalty-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 9999;
    display: none;
}

#update-penalty-modal .modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 20px;
    border-radius: 5px;
    min-width: 300px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

#update-penalty-modal .modal-content h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #23282d;
}

#update-penalty-modal .modal-content label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

#update-penalty-modal .modal-content select {
    width: 100%;
    padding: 8px;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 3px;
}

#update-penalty-modal .modal-actions {
    margin-top: 15px;
    text-align: right;
}

#update-penalty-modal .modal-actions button {
    margin-left: 10px;
}

/* Tracking Tab Styles */
.auction-penalty-tracking .expired {
    background-color: #f8d7da !important;
}

.auction-penalty-tracking .expired-label {
    color: #dc3545;
    font-weight: bold;
    font-size: 0.8em;
    background: #fff;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 10px;
}

/* Settings Tab Styles */
.auction-penalty-settings .settings-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.auction-penalty-settings .settings-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #23282d;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.auction-penalty-settings .form-table th {
    font-weight: bold;
    color: #23282d;
}

/* General Styles */
.auction-penalty-warning {
    margin: 20px 0;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid;
}

.penalty-level-1 {
    background-color: #fff3cd;
    border-color: #ffc107;
    color: #856404;
}

.penalty-level-2 {
    background-color: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.penalty-level-3 {
    background-color: #d1ecf1;
    border-color: #17a2b8;
    color: #0c5460;
}

.auction-penalty-warning h4 {
    margin: 0 0 10px 0;
    font-weight: bold;
}

.auction-penalty-warning p {
    margin: 5px 0;
}

.penalty-reason {
    font-style: italic;
    font-size: 0.9em;
}

.deposit-payment-section {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid rgba(0,0,0,0.1);
}

.deposit-payment-btn {
    background-color: #007cba;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 14px;
}

.deposit-payment-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.deposit-payment-btn:hover:not(:disabled) {
    background-color: #005a87;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auction-penalty-overview .stats-grid {
        grid-template-columns: 1fr;
    }
    
    #update-penalty-modal .modal-content {
        width: 90%;
        min-width: auto;
    }
    
    .auction-penalty-overview .cron-info code {
        font-size: 12px;
        word-break: break-all;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.notice-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.notice-error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

/* Table Enhancements */
.wp-list-table .column-actions {
    width: 150px;
}

.wp-list-table .button-small {
    padding: 4px 8px;
    font-size: 12px;
    margin-right: 5px;
}

/* Tab Content */
.tab-content {
    margin-top: 20px;
}
