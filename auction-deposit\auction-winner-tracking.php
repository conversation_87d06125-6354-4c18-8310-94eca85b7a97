<?php
/**
 * Auction Winner Tracking
 * 
 * Handles tracking of auction winners and their purchase status
 * 
 * @package AuctionPenaltySystem
 * @version 1.0.0
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Auction Winner Tracker Class
 */
class AuctionWinnerTracker {
    
    /**
     * Track auction winner when auction finishes
     */
    public function track_auction_winner($product_id) {
        $product = wc_get_product($product_id);
        
        if (!$product || !$product->is_type('auction')) {
            return false;
        }
        
        // Get auction winner
        $winner_id = get_post_meta($product_id, '_auction_current_bider', true);
        
        if (!$winner_id) {
            return false;
        }
        
        // Check if already tracked
        if ($this->is_winner_already_tracked($product_id, $winner_id)) {
            return false;
        }
        
        // Create tracking record
        return $this->create_tracking_record($product_id, $winner_id);
    }
    
    /**
     * Check if winner is already being tracked
     */
    private function is_winner_already_tracked($product_id, $winner_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'auction_winner_tracking';
        
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE product_id = %d AND winner_id = %d",
            $product_id, $winner_id
        ));
        
        return !empty($existing);
    }
    
    /**
     * Create tracking record
     */
    private function create_tracking_record($product_id, $winner_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'auction_winner_tracking';
        
        // Ensure table exists
        $this->create_tracking_table();
        
        $result = $wpdb->insert(
            $table_name,
            array(
                'product_id' => $product_id,
                'winner_id' => $winner_id,
                'auction_end_time' => current_time('mysql'),
                'purchase_deadline' => date('Y-m-d H:i:s', strtotime('+24 hours')),
                'status' => 'pending'
            ),
            array('%d', '%d', '%s', '%s', '%s')
        );
        
        if ($result) {
            // Send notification to winner
            $this->send_winner_notification($product_id, $winner_id);
            
            error_log("Auction Winner Tracker: Created tracking record for product {$product_id}, winner {$winner_id}");
            return true;
        }
        
        return false;
    }
    
    /**
     * Send notification to auction winner
     */
    private function send_winner_notification($product_id, $winner_id) {
        $user = get_userdata($winner_id);
        $product = wc_get_product($product_id);
        
        if (!$user || !$product) {
            return;
        }
        
        // Get user email
        $user_email = $user->user_email;
        
        if (!$user_email) {
            return;
        }
        
        // Prepare email content
        $subject = sprintf(__('Congratulations! You won the auction for %s', 'woocommerce'), $product->get_name());
        
        $message = sprintf(
            __('Dear %s,

Congratulations! You have won the auction for "%s".

You have 24 hours to complete your purchase. If you do not purchase within this time, you will receive a penalty and the item will be offered to the next highest bidder.

Product: %s
Your winning bid: %s
Purchase deadline: %s

Click here to complete your purchase: %s

Thank you for participating in our auction!', 'woocommerce'),
            $user->display_name,
            $product->get_name(),
            $product->get_name(),
            wc_price(get_post_meta($product_id, '_auction_current_bid', true)),
            date('Y-m-d H:i:s', strtotime('+24 hours')),
            get_permalink($product_id)
        );
        
        // Send email
        wp_mail($user_email, $subject, $message);
        
        error_log("Auction Winner Tracker: Sent notification to {$user_email} for auction {$product_id}");
    }
    
    /**
     * Mark winner as purchased
     */
    public function mark_as_purchased($product_id, $winner_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'auction_winner_tracking';
        
        $result = $wpdb->update(
            $table_name,
            array('status' => 'purchased'),
            array(
                'product_id' => $product_id,
                'winner_id' => $winner_id,
                'status' => 'pending'
            ),
            array('%s'),
            array('%d', '%d', '%s')
        );
        
        if ($result) {
            error_log("Auction Winner Tracker: Marked as purchased - product {$product_id}, winner {$winner_id}");
        }
        
        return $result;
    }
    
    /**
     * Get winner tracking status
     */
    public function get_winner_status($product_id, $winner_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'auction_winner_tracking';
        
        $status = $wpdb->get_var($wpdb->prepare(
            "SELECT status FROM $table_name WHERE product_id = %d AND winner_id = %d ORDER BY id DESC LIMIT 1",
            $product_id, $winner_id
        ));
        
        return $status;
    }
    
    /**
     * Get all pending winners
     */
    public function get_pending_winners() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'auction_winner_tracking';
        
        return $wpdb->get_results("
            SELECT * FROM $table_name 
            WHERE status = 'pending' 
            ORDER BY purchase_deadline ASC
        ");
    }
    
    /**
     * Get expired winners (past deadline)
     */
    public function get_expired_winners() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'auction_winner_tracking';
        
        return $wpdb->get_results($wpdb->prepare("
            SELECT * FROM $table_name 
            WHERE status = 'pending' 
            AND purchase_deadline < %s
            ORDER BY purchase_deadline ASC
        ", current_time('mysql')));
    }
    
    /**
     * Get winner tracking history for a user
     */
    public function get_user_tracking_history($user_id, $limit = 10) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'auction_winner_tracking';
        
        return $wpdb->get_results($wpdb->prepare("
            SELECT wt.*, p.post_title as product_name
            FROM $table_name wt
            LEFT JOIN {$wpdb->posts} p ON wt.product_id = p.ID
            WHERE wt.winner_id = %d
            ORDER BY wt.date_created DESC
            LIMIT %d
        ", $user_id, $limit));
    }
    
    /**
     * Clean up old tracking records (older than 30 days)
     */
    public function cleanup_old_records() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'auction_winner_tracking';
        
        $deleted = $wpdb->query($wpdb->prepare("
            DELETE FROM $table_name 
            WHERE date_created < %s
        ", date('Y-m-d H:i:s', strtotime('-30 days'))));
        
        if ($deleted) {
            error_log("Auction Winner Tracker: Cleaned up {$deleted} old tracking records");
        }
        
        return $deleted;
    }
    
    /**
     * Create tracking table
     */
    private function create_tracking_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'auction_winner_tracking';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            product_id bigint(20) NOT NULL,
            winner_id bigint(20) NOT NULL,
            auction_end_time datetime NOT NULL,
            purchase_deadline datetime NOT NULL,
            status varchar(20) DEFAULT 'pending',
            date_created datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY product_id (product_id),
            KEY winner_id (winner_id),
            KEY status (status),
            KEY purchase_deadline (purchase_deadline)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Get statistics
     */
    public function get_statistics() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'auction_winner_tracking';
        
        $stats = array();
        
        // Total tracked winners
        $stats['total_tracked'] = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        
        // Purchased within deadline
        $stats['purchased'] = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'purchased'");
        
        // Penalties applied
        $stats['penalties'] = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'penalty_applied'");
        
        // Currently pending
        $stats['pending'] = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'pending'");
        
        // Success rate
        $stats['success_rate'] = $stats['total_tracked'] > 0 ? 
            round(($stats['purchased'] / $stats['total_tracked']) * 100, 2) : 0;
        
        return $stats;
    }
}
