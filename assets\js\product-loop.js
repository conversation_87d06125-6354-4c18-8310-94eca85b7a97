jQuery(document).ready(function($) {
    // Function to add title attributes
    function addTitleAttributes() {
        $('.woocommerce-loop-product__title, .auction-product-title').each(function() {
            var fullTitle = $(this).text().trim();
            $(this).attr('title', fullTitle);
        });
    }
    
    // Run immediately for static content
    addTitleAttributes();
    
    // Run again after a short delay to catch dynamically loaded content
    setTimeout(addTitleAttributes, 1000);
    
    // If using Owl Carousel, run after initialization
    $(document).on('initialized.owl.carousel', function() {
        addTitleAttributes();
    });
    
    // For any AJAX-loaded content
    $(document).ajaxComplete(function() {
        addTitleAttributes();
    });
}); 