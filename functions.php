<?php
// Exit if accessed directly
if ( !defined( 'ABSPATH' ) ) exit;
 
if ( !function_exists( 'chld_thm_cfg_locale_css' ) ):
    function chld_thm_cfg_locale_css( $uri ){
        if ( empty( $uri ) && is_rtl() && file_exists( get_template_directory() . '/rtl.css' ) )
            $uri = get_template_directory_uri() . '/rtl.css';
        return $uri;
    }
endif;
add_filter( 'locale_stylesheet_uri', 'chld_thm_cfg_locale_css' );

function allow_svg_upload($mimes) {
    $mimes['svg'] = 'image/svg+xml';
    return $mimes;
}
add_filter('upload_mimes', 'allow_svg_upload');


/*
Enqueue scripts and styles
*/
function olb_enqueue_scripts() {
    if ( is_singular('product') ) {
        wp_enqueue_style('olb-product-single', get_stylesheet_directory_uri() . '/assets/css/product-single.css', array(), filemtime(get_stylesheet_directory() . '/assets/css/product-single.css'));
        wp_enqueue_script('olb-product-single', get_stylesheet_directory_uri() . '/assets/js/product-single.js', array('jquery'), filemtime(get_stylesheet_directory() . '/assets/js/product-single.js'), true);
    }
    // account-page.css
    if (is_account_page()) {
        wp_enqueue_style('olb-account-page', get_stylesheet_directory_uri() . '/assets/css/account-page.css', array(), filemtime(get_stylesheet_directory() . '/assets/css/account-page.css'));
    }
}
add_action('wp_enqueue_scripts', 'olb_enqueue_scripts');
/**
 * WooCommerce Main Categories Shortcode with Transient Cache
 */
function olb_main_categories_shortcode() {
    // Check for existing transient
    $cached_output = get_transient('olb_main_categories_output');
    if (false !== $cached_output) {
        return $cached_output;
    }
    
    // Get top-level product categories
    $args = array(
        'taxonomy'     => 'product_cat',
        'orderby'      => 'name',
        'hide_empty'   => false,
        'parent'       => 0
    );
    $categories = get_terms($args);
    
    // Start output buffer
    ob_start();
    
    if (!empty($categories)) {
        echo '<div class="woo-main-categories">';
        
        foreach ($categories as $category) {
            // Get category image
            $thumbnail_id = get_term_meta($category->term_id, 'thumbnail_id', true);
            $image = wp_get_attachment_url($thumbnail_id);
            
            // Fallback image if no thumbnail
            if (!$image) {
                $image = wc_placeholder_img_src();
            }
            
            // Category link
            $link = get_term_link($category, 'product_cat');
            
            echo '<div class="category-item">';
            echo '<a href="' . esc_url($link) . '">';
            echo '<div class="category-icon"><img src="' . esc_url($image) . '" alt="' . esc_attr($category->name) . '"></div>';
            echo '<div class="category-name">' . esc_html($category->name) . '</div>';
            echo '</a>';
            echo '</div>';
        }
        
        echo '</div>';
    }
    
    // Get output and store in transient (cache for 12 hours)
    $output = ob_get_clean();
    set_transient('olb_main_categories_output', $output, 12 * HOUR_IN_SECONDS);
    
    return $output;
}
add_shortcode('olb_main_categories', 'olb_main_categories_shortcode');

/**
 * Clear categories transient when categories are modified
 */
function clear_olb_categories_transient() {
    delete_transient('olb_main_categories_output');
}
add_action('edited_product_cat', 'clear_olb_categories_transient');
add_action('created_product_cat', 'clear_olb_categories_transient');
add_action('delete_product_cat', 'clear_olb_categories_transient');

/**
 * Add CSS for main categories display
 */
function olb_main_categories_styles() {
    ?>
    <style>
        .woo-main-categories {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 15px;
            margin: 20px 0;
            width: 100%;
        }
        .woo-main-categories .category-item {
            text-align: center;
            width: 100px;
        }
        .woo-main-categories .category-item a {
            text-decoration: none;
        }
        .woo-main-categories .category-icon {
            background: #f5f5f5;
            border-radius: 50%;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            overflow: hidden;
        }
        .woo-main-categories .category-icon img {
            max-width: 60%;
            max-height: 60%;
            object-fit: contain;
        }
        .woo-main-categories .category-name {
           
        }
        .woo-main-categories .category-name  { 
            font-family: 'Manrope';
            font-style: normal;
            font-weight: 700;
            font-size: 14px;
            line-height: 20px;  
            color: #121217; 
            text-decoration: none; 
        }
    </style>
    <?php
}
add_action('wp_head', 'olb_main_categories_styles');

add_shortcode( 'gp_nav', 'tct_gp_nav' );
function tct_gp_nav( $atts ) {
    ob_start();
    generate_navigation_position();
    return ob_get_clean();
} 

add_filter('walker_nav_menu_start_el', 'olb_add_mega_menu_shortcode', 10, 4);
function olb_add_mega_menu_shortcode($item_output, $item, $depth, $args) { 
    // ACF / post_meta'dan çek
    $mega_menu_shortcode = get_post_meta( $item->ID, 'mega_menu_shortcode', true );

    // Eğer alt menüdeyse ve mega_menu_shortcode doluysa
    if ( $mega_menu_shortcode && $depth > 0 ) { 
        // Burada menü çıktısını tamamen temizleyip (title'ı, linki vs. hepsini)
        // sadece shortcode sonucunu basabiliriz.
        $item_output = '<div class="mega-menu-shortcode-content">' 
                        . do_shortcode($mega_menu_shortcode) 
                       . '</div>';
    }

    return $item_output;
} 

// add loggedin only menu items 
function add_custom_menu_fields($item_id, $item) {
    $value = get_post_meta($item_id, '_menu_item_loggedin_only', true);
    ?>
    <p class="description description-wide">
        <label for="edit-menu-item-loggedin-only-<?php echo $item_id; ?>">
            <input type="checkbox" id="edit-menu-item-loggedin-only-<?php echo $item_id; ?>" name="menu-item-loggedin-only[<?php echo $item_id; ?>]" value="1" <?php checked($value, '1'); ?> />
            Show only for logged in users
        </label>
    </p>
    <?php
}
add_action('wp_nav_menu_item_custom_fields', 'add_custom_menu_fields', 10, 2);

function save_custom_menu_fields($menu_id, $menu_item_db_id) {
    if (isset($_POST['menu-item-loggedin-only'][$menu_item_db_id])) {
        update_post_meta($menu_item_db_id, '_menu_item_loggedin_only', '1');
    } else {
        delete_post_meta($menu_item_db_id, '_menu_item_loggedin_only');
    }
}
add_action('wp_update_nav_menu_item', 'save_custom_menu_fields', 10, 2);

function filter_menu_items($items, $args) {
    foreach ($items as $key => $item) {
        $loggedin_only = get_post_meta($item->ID, '_menu_item_loggedin_only', true);
        if ($loggedin_only && !is_user_logged_in()) {
            unset($items[$key]);
        }
    }
    return $items;
}
add_filter('wp_get_nav_menu_items', 'filter_menu_items', 10, 2);

// Course Menu Shortcode
function mega_menu_shortcode() {
    add_shortcode('mega_menu', 'mega_menu_shortcode_callback');
}
add_action('init', 'mega_menu_shortcode');

function mega_menu_shortcode_callback() {
    ob_start();
    do_action('mega_menu_shortcode');
    return ob_get_clean();
}
 
// Hook içeriğini doldur
add_action('mega_menu_shortcode', function() {
    if (function_exists('generate_elements_get_content')) {
        $element_id = get_post_meta(get_the_ID(), 'mega_menu_element_id', true);
        
        if ($element_id) {
            echo generate_elements_get_content($element_id);
        }
    }
});
 
/**
 * Shortcode to display auction products in a slider
 * Usage: [auction_products_slider count="5" columns="5"]
 */
function olb_auction_products_slider_shortcode($atts) {
    // Parse attributes
    $atts = shortcode_atts(array(
        'count' => 5,     // Toplam gösterilecek ürün sayısı
        'columns' => 5,   // Ekranda aynı anda gösterilecek ürün sayısı
    ), $atts);
    
    // Enqueue required scripts and styles
    wp_enqueue_script('jquery');
    wp_enqueue_script('owl-carousel', 'https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js', array('jquery'), '2.3.4', true);
    wp_enqueue_style('owl-carousel', 'https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css');
    wp_enqueue_style('owl-theme', 'https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css');
    wp_enqueue_style('auctions-template-css', get_stylesheet_directory_uri() . '/assets/css/auctions-template.css', array(), filemtime(get_stylesheet_directory() . '/assets/css/auctions-template.css'));
    
    // Start output buffer
    ob_start();
    
    // Query auction products
    $args = array(
        'post_type' => 'product',
        'posts_per_page' => intval($atts['count']),
        'tax_query' => array(
            array(
                'taxonomy' => 'product_type',
                'field' => 'slug',
                'terms' => 'auction'
            )
        ),
        'meta_query' => array(
            array(
                'key' => '_auction_closed',
                'compare' => 'NOT EXISTS'
            )
        ),
        'orderby' => 'meta_value',
        'meta_key' => '_auction_dates_to',
        'order' => 'ASC',
    );
    
    $products = new WP_Query($args);
    
    if ($products->have_posts()) {
        $slider_id = 'auction-slider-' . uniqid();
        $columns = intval($atts['columns']);
        
        ?>
        <div class="auction-slider-container">
            <div class="owl-carousel owl-theme" id="<?php echo esc_attr($slider_id); ?>">
                <?php
                while ($products->have_posts()) {
                    $products->the_post();
                    global $product;
                    ?>
                    <div class="item">
                        <div class="auction-product">
                            <div class="auction-product-image">
                                <a href="<?php echo esc_url(get_permalink()); ?>">
                                    <?php echo woocommerce_get_product_thumbnail(); ?>
                                </a>
                            </div>
                            
                            <h3 class="auction-product-title" title="<?php echo esc_attr(get_the_title()); ?>">
                                <a href="<?php echo esc_url(get_permalink()); ?>"><?php the_title(); ?></a>
                            </h3>
                            
                            <?php if (method_exists($product, 'get_curent_bid')): ?>
                            <div class="auction-current-bid">
                                <span class="auction-price-label">Mevcut teklif fiyatı: </span>
                                <span class="auction-price"><?php echo wc_price($product->get_curent_bid()); ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <div class="auction-starting-price">
                                <span class="auction-starting-price-label">Açık artırma fiyatı: </span>
                                <span class="auction-starting-price-value">1₺</span>
                            </div>
                            
                            <div class="auction-time-remaining"> 
                                <?php wc_get_template('global/auction-counter.php'); ?>
                            </div>
                            
                            <div class="auction-bid-button">
                                <a href="<?php echo esc_url(get_permalink()); ?>" class="button">Teklif Ver <svg width="9" height="10" viewBox="0 0 9 10" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M1.16699 1.66675H7.83366M7.83366 1.66675V8.33341M7.83366 1.66675L1.16699 8.33341" stroke="#F9F9FB" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</a>
                            </div>
                        </div>
                    </div>
                    <?php
                }
                ?>
            </div>
            
            <div class="custom-nav">
    <button class="custom-prev"><i></i></button>
    <button class="custom-next"><i></i></button>
</div>
        </div>
        
        <script>
            jQuery(document).ready(function($) {
                $("#<?php echo esc_js($slider_id); ?>").owlCarousel({
                    loop: false,
                    margin: 20,
                    nav: false,
                    dots: false,
                    autoplay: true,
                    autoplayTimeout: 5000,
                    autoplayHoverPause: true,
                    responsive: {
                        0: { items: 1 },
                        480: { items: 2 },
                        768: { items: 3 },
                        992: { items: 4 },
                        1200: { items: <?php echo $columns; ?> }
                    }
                });
                
                $(".custom-prev").click(function() {
                    $("#<?php echo esc_js($slider_id); ?>").trigger('prev.owl.carousel');
                });
                
                $(".custom-next").click(function() {
                    $("#<?php echo esc_js($slider_id); ?>").trigger('next.owl.carousel');
                });
            });
        </script>
        <?php
    } else {
        echo '<p>' . __('Açık artırma ürünü bulunamadı.', 'woocommerce') . '</p>';
    }
    
    wp_reset_postdata();
    
    // Add inline script to handle tooltips specifically for this slider
    ?>
    <script>
    jQuery(document).ready(function($) {
        // Wait for the slider to be fully initialized
        $('#<?php echo esc_js($slider_id); ?>').on('initialized.owl.carousel', function() {
            // Add title attributes to auction product titles
            $('#<?php echo esc_js($slider_id); ?> .auction-product-title').each(function() {
                var fullTitle = $(this).text().trim();
                $(this).attr('title', fullTitle);
            });
        });
    });
    </script>
    <?php
    
    // Return the output buffer
    $output = ob_get_clean();
    return $output;
}
add_shortcode('auction_products_slider', 'olb_auction_products_slider_shortcode');

/**
 * Clear auction slider transient when products are modified
 */
function clear_olb_auction_slider_transient() {
    global $wpdb;
    $wpdb->query("DELETE FROM $wpdb->options WHERE option_name LIKE '%_transient_olb_auction_slider_%'");
}
add_action('save_post_product', 'clear_olb_auction_slider_transient');
add_action('woocommerce_delete_product', 'clear_olb_auction_slider_transient');

/**
 * Function to manually clear all auction slider transients
 */
function olb_clear_all_auction_slider_transients() {
    global $wpdb;
    $deleted = $wpdb->query("DELETE FROM $wpdb->options WHERE option_name LIKE '%_transient_olb_auction_slider_%'");
    return $deleted;
}

// You can call this function directly when needed:
 //olb_clear_all_auction_slider_transients();
 /* Sample */
 function olb_my_bids_button_shortcode( $atts ) {
    ob_start();
    global $wpdb;
    $user_id = get_current_user_id();
    
    // Transient anahtarı oluştur
    $transient_key = 'olb_user_bids_count_' . $user_id;
    
    // Transient'i kontrol et
    $useractivity_count = get_transient($transient_key);
    
    // Eğer transient yoksa veya süresi dolmuşsa, veritabanından çek
    if (false === $useractivity_count) {
        $count_result = $wpdb->get_var($wpdb->prepare(
            'SELECT COUNT(*) FROM `' . $wpdb->prefix . 'auctions_for_woocommerce_log` WHERE `userid` = %d',
            $user_id
        ));
        
        // Sonucu transient'e kaydet (12 saat geçerli)
        set_transient($transient_key, $count_result, 12 * HOUR_IN_SECONDS);
        $useractivity_count = $count_result;
    }
    
    $atts = shortcode_atts(array(
        'url' => '#'
    ), $atts);
    ?>
    <a href="<?php echo esc_url(home_url($atts['url'])); ?>" class="button user-activity-button">
        <div class="user-activity-count-container">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M11.8059 0C15.0219 0 17.6658 2.52709 17.8 5.72919H17.7686C17.7724 5.82227 17.7545 5.91495 17.7164 6H17.9037C19.3642 6 20.8934 7.01221 21.5066 9.45576L21.5732 9.74409L22.4959 17.1777C23.1599 21.9189 20.566 23.9127 17.0274 23.9972L16.7902 24H6.84223C3.24613 24 0.475053 22.6896 1.08434 17.5003L1.12589 17.1777L2.05917 9.74409C2.51937 7.11262 4.06433 6.0747 5.55294 6.00392L5.71805 6H5.81186C5.79604 5.91042 5.79604 5.81877 5.81186 5.72919C5.94607 2.52709 8.58999 0 11.8059 0ZM8.31639 9.99515C7.73067 9.99515 7.25585 10.4839 7.25585 11.0867C7.25585 11.6896 7.73067 12.1783 8.31639 12.1783C8.90212 12.1783 9.37694 11.6896 9.37694 11.0867L9.36868 10.9498C9.30324 10.4116 8.85706 9.99515 8.31639 9.99515ZM15.263 9.99515C14.6773 9.99515 14.2024 10.4839 14.2024 11.0867C14.2024 11.6896 14.6773 12.1783 15.263 12.1783C15.8487 12.1783 16.3235 11.6896 16.3235 11.0867C16.3235 10.4839 15.8487 9.99515 15.263 9.99515ZM11.7589 1.56286C9.44997 1.56286 7.57821 3.42819 7.57821 5.72919C7.59403 5.81877 7.59403 5.91042 7.57821 6H15.9918C15.9585 5.91353 15.9408 5.82183 15.9396 5.72919C15.9396 3.42819 14.0678 1.56286 11.7589 1.56286Z" fill="#050632"/>
        </svg> 
        <span class="user-activity-count"><?php echo intval($useractivity_count); ?></span>
        </div>
        <?php _e('Tekliflerim', 'woocommerce'); ?>
    </a>
    <?php
    return ob_get_clean();
 }
 add_shortcode('olb_my_bids_button', 'olb_my_bids_button_shortcode');

/**
 * Kullanıcı teklif sayısı transient'ini temizle
 */
function olb_clear_user_bids_count_transient($user_id = 0) {
    // Eğer user_id belirtilmemişse, mevcut kullanıcıyı al
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    // Kullanıcı giriş yapmışsa transient'i temizle
    if ($user_id > 0) {
        delete_transient('olb_user_bids_count_' . $user_id);
    }
}

// İlgili hook'lar için transient temizleme işlemini ekle
add_action('auctions_for_woocommerce_before_place_bid', 'olb_clear_user_bids_count_transient');
add_action('auctions_for_woocommerce_outbid', 'olb_clear_user_bids_count_transient');
add_action('auctions_for_woocommerce_place_bid', 'olb_clear_user_bids_count_transient');
add_action('auctions_for_woocommerce_delete_bid', 'olb_clear_user_bids_count_transient');

// Açık artırma durumu değiştiğinde de temizle (isteğe bağlı)
add_action('auctions_for_woocommerce_won', 'olb_clear_user_bids_count_transient');
add_action('auctions_for_woocommerce_finished', 'olb_clear_user_bids_count_transient');
add_action('auctions_for_woocommerce_close', 'olb_clear_user_bids_count_transient');

/**
 * Kullanıcı avatar yükleme ve görüntüleme fonksiyonları
 */
function olb_add_avatar_upload_field() {
    // WooCommerce hesap sayfasına avatar yükleme alanı ekle
    add_action('woocommerce_edit_account_form', 'olb_avatar_upload_field');
    
    // Form enctype özelliğini ekle
    add_action('woocommerce_edit_account_form_start', 'olb_add_form_enctype');
    
    // Avatar yükleme işlemini kaydet
    add_action('woocommerce_save_account_details', 'olb_save_avatar_upload');
    
    // Avatar CSS stillerini ekle
    add_action('wp_head', 'olb_avatar_styles');
}
add_action('init', 'olb_add_avatar_upload_field');

/**
 * Avatar yükleme alanını göster
 */
function olb_avatar_upload_field() {
    $user_id = get_current_user_id();
    $avatar_url = get_user_meta($user_id, 'olb_user_avatar_medium', true);
    if (!$avatar_url) {
        $avatar_url = get_user_meta($user_id, 'olb_user_avatar', true);
    }
    ?>
    <p class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide">
        <label for="olb_user_avatar"><?php _e('Profil Fotoğrafı', 'woocommerce'); ?></label>
        
        <?php if ($avatar_url) : ?>
            <div class="olb-current-avatar">
                <img src="<?php echo esc_url($avatar_url); ?>" alt="<?php _e('Mevcut Avatar', 'woocommerce'); ?>" />
            </div>
        <?php endif; ?>
        
        <input type="file" class="woocommerce-Input" name="olb_user_avatar" id="olb_user_avatar" accept="image/*" />
        <span><em><?php _e('Profil fotoğrafınızı yükleyin (JPG, PNG veya GIF)', 'woocommerce'); ?></em></span>
    </p>
    <?php
}

/**
 * Form enctype özelliğini ekle
 */
function olb_add_form_enctype() {
    ?>
    <script type="text/javascript">
        jQuery(document).ready(function($) {
            $('form.woocommerce-EditAccountForm').attr('enctype', 'multipart/form-data');
        });
    </script>
    <?php
}

/**
 * Avatar yükleme işlemini kaydet ve farklı boyutlarda kaydet
 */
function olb_save_avatar_upload($user_id) {
    // Dosya yükleme kontrolü
    if (isset($_FILES['olb_user_avatar']) && !empty($_FILES['olb_user_avatar']['name'])) {
        // WordPress medya yükleme işlevlerini dahil et
        if (!function_exists('wp_handle_upload')) {
            require_once(ABSPATH . 'wp-admin/includes/file.php');
        }
        
        if (!function_exists('wp_generate_attachment_metadata')) {
            require_once(ABSPATH . 'wp-admin/includes/image.php');
        }
        
        // Hata ayıklama için
        error_log('Avatar yükleme başlatıldı: ' . print_r($_FILES['olb_user_avatar'], true));
        
        $upload_overrides = array('test_form' => false);
        $uploaded_file = $_FILES['olb_user_avatar'];
        
        // Dosya uzantısını kontrol et
        $file_ext = strtolower(pathinfo($uploaded_file['name'], PATHINFO_EXTENSION));
        $allowed_exts = array('jpg', 'jpeg', 'png', 'gif');
        
        if (!in_array($file_ext, $allowed_exts)) {
            wc_add_notice(__('Lütfen geçerli bir resim dosyası yükleyin (JPG, PNG veya GIF).', 'woocommerce'), 'error');
            return;
        }
        
        // Dosyayı yükle
        $movefile = wp_handle_upload($uploaded_file, $upload_overrides);
        
        if ($movefile && !isset($movefile['error'])) {
            // Eski avatarı sil
            $old_avatar_url = get_user_meta($user_id, 'olb_user_avatar', true);
            if ($old_avatar_url) {
                $old_avatar_path = str_replace(wp_upload_dir()['baseurl'], wp_upload_dir()['basedir'], $old_avatar_url);
                if (file_exists($old_avatar_path)) {
                    @unlink($old_avatar_path);
                }
                
                // Küçük boyutlu eski avatarı da sil
                $old_avatar_small_url = get_user_meta($user_id, 'olb_user_avatar_small', true);
                if ($old_avatar_small_url) {
                    $old_avatar_small_path = str_replace(wp_upload_dir()['baseurl'], wp_upload_dir()['basedir'], $old_avatar_small_url);
                    if (file_exists($old_avatar_small_path)) {
                        @unlink($old_avatar_small_path);
                    }
                }
            }
            
            // Yeni avatar URL'sini kaydet
            update_user_meta($user_id, 'olb_user_avatar', $movefile['url']);
            
            // Küçük boyutlu avatar oluştur (28x28)
            $image_path = $movefile['file'];
            $upload_dir = wp_upload_dir();
            $image_info = pathinfo($image_path);
            $small_image_name = $image_info['filename'] . '-small.' . $image_info['extension'];
            $small_image_path = $upload_dir['path'] . '/' . $small_image_name;
            $small_image_url = $upload_dir['url'] . '/' . $small_image_name;
            
            // Resmi yeniden boyutlandır
            $editor = wp_get_image_editor($image_path);
            if (!is_wp_error($editor)) {
                $editor->resize(28, 28, true); // 28x28 boyutunda kırp
                $editor->save($small_image_path);
                update_user_meta($user_id, 'olb_user_avatar_small', $small_image_url);
                
                // Orta boyutlu avatar oluştur (92x92)
                $medium_image_name = $image_info['filename'] . '-medium.' . $image_info['extension'];
                $medium_image_path = $upload_dir['path'] . '/' . $medium_image_name;
                $medium_image_url = $upload_dir['url'] . '/' . $medium_image_name;
                
                $editor = wp_get_image_editor($image_path); // Yeni bir editor örneği al
                if (!is_wp_error($editor)) {
                    $editor->resize(92, 92, true); // 92x92 boyutunda kırp
                    $editor->save($medium_image_path);
                    update_user_meta($user_id, 'olb_user_avatar_medium', $medium_image_url);
                }
            }
            
            wc_add_notice(__('Profil fotoğrafınız başarıyla güncellendi.', 'woocommerce'), 'success');
            
            // Hata ayıklama için
            error_log('Avatar başarıyla yüklendi: ' . $movefile['url']);
        } else {
            $error_msg = isset($movefile['error']) ? $movefile['error'] : 'Bilinmeyen hata';
            wc_add_notice(__('Dosya yüklenirken bir hata oluştu.', 'woocommerce') . ' ' . $error_msg, 'error');
            
            // Hata ayıklama için
            error_log('Avatar yükleme hatası: ' . $error_msg);
        }
    }
}

/**
 * Avatar CSS stillerini ekle
 */
function olb_avatar_styles() {
    ?>
    <style>
        .olb-current-avatar {
            margin-bottom: 10px;
        }
        .olb-current-avatar img {
            width: 92px;
            height: 92px;
            border-radius: 50%;
            object-fit: cover;
        }
        .olb-user-avatar-small {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 8px;
        }
        .olb-user-avatar-medium {
            width: 92px;
            height: 92px;
            border-radius: 50%;
            object-fit: cover;
        }
        .user-account-button {
            display: flex;
            align-items: center;
        }
    </style>
    <?php
}

/**
 * Kullanıcı avatar görüntüleme fonksiyonu
 */
function olb_get_user_avatar($user_id = 0, $size = 'small') {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if ($size == 'small') {
        $avatar_url = get_user_meta($user_id, 'olb_user_avatar_small', true);
        if (!$avatar_url) {
            $avatar_url = get_user_meta($user_id, 'olb_user_avatar', true);
        }
        $class = 'olb-user-avatar-small';
    } else {
        $avatar_url = get_user_meta($user_id, 'olb_user_avatar_medium', true);
        if (!$avatar_url) {
            $avatar_url = get_user_meta($user_id, 'olb_user_avatar', true);
        }
        $class = 'olb-user-avatar-medium';
    }
    
    if (!$avatar_url) {
        // Varsayılan avatar
        $avatar_url = get_stylesheet_directory_uri() . '/assets/img/avatar-default.svg';
    }
    
    return '<img src="' . esc_url($avatar_url) . '" alt="' . __('Kullanıcı Avatarı', 'woocommerce') . '" class="' . $class . '" />';
}

/**
 * Kullanıcı giriş butonunu güncelle (avatar ekle)
 */
function olb_user_login_button_shortcode($atts) {
    ob_start();
    $atts = shortcode_atts(array(
        'url' => '#'
    ), $atts);
    
    if (is_user_logged_in()) {
        $user_id = get_current_user_id();
        $user_data = get_userdata($user_id);
        $user_name = $user_data->first_name . ' ' . $user_data->last_name;
        if (empty(trim($user_name))) {
            $user_name = $user_data->display_name;
        }
        ?>
        <?php echo olb_my_bids_button_shortcode(array('url' => 'tekliflerim/')); ?>
        <div class="user-login-button-container">
            <a href="<?php echo esc_url(wc_get_account_endpoint_url('account')); ?>" class="user-account-button">
                <?php echo olb_get_user_avatar($user_id, 'small'); ?>
                <?php _e('Hesabım', 'woocommerce'); ?>
                <svg width="14" height="8" viewBox="0 0 14 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 1L7 7L13 1" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg> 
            </a>
            <div class="user-login-menu-container">
                <ul class="user-login-menu">
                    <li><a href="<?php echo esc_url(wc_get_account_endpoint_url('orders')); ?>"><img src="<?php echo get_stylesheet_directory_uri() . '/assets/img/shopping-bag.svg'; ?>" alt="<?php _e('Siparişlerim', 'woocommerce'); ?>"><?php _e('Siparişlerim', 'woocommerce'); ?></a></li>
                    <li><a href="<?php echo esc_url(home_url('takip-listem/')); ?>"><img src="<?php echo get_stylesheet_directory_uri() . '/assets/img/eye.svg'; ?>" alt="<?php _e('Takip Listem', 'woocommerce'); ?>"><?php _e('Takip Listem', 'woocommerce'); ?></a></li>
                    <li><a href="<?php echo esc_url(home_url('takip-listem/')); ?>"><img src="<?php echo get_stylesheet_directory_uri() . '/assets/img/user-round.svg'; ?>" alt="<?php _e('Kullanıcı Bilgilerim', 'woocommerce'); ?>"><?php _e('Kullanıcı Bilgilerim', 'woocommerce'); ?></a></li>
                    <li><a href="<?php echo esc_url(wp_logout_url()); ?>"><img src="<?php echo get_stylesheet_directory_uri() . '/assets/img/log-out.svg'; ?>" alt="<?php _e('Çıkış Yap', 'woocommerce'); ?>"><?php _e('Çıkış Yap', 'woocommerce'); ?></a></li>
                </ul>
            </div>
        </div>
    <?php
    } else {
        ?>
        <a href="<?php echo esc_url(wc_get_account_endpoint_url('/')); ?>" class="button login-button user-account-button">
        <svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M6.00033 7.66667C7.84127 7.66667 9.33366 6.17428 9.33366 4.33333C9.33366 2.49238 7.84127 1 6.00033 1C4.15938 1 2.66699 2.49238 2.66699 4.33333C2.66699 6.17428 4.15938 7.66667 6.00033 7.66667ZM6.00033 7.66667C7.41481 7.66667 8.77137 8.22857 9.77156 9.22876C10.7718 10.229 11.3337 11.5855 11.3337 13M6.00033 7.66667C4.58584 7.66667 3.22928 8.22857 2.22909 9.22876C1.2289 10.229 0.666992 11.5855 0.666992 13" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

            <?php _e('Giriş Yap', 'woocommerce'); ?>
        </a>
    <?php
    }
    return ob_get_clean();
}
add_shortcode('olb_user_login_button', 'olb_user_login_button_shortcode');

/**
 * Kullanıcı menüsü için JavaScript ekle
 */
function olb_user_menu_scripts() {
    ?>
    <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Menü dışına tıklandığında menüyü kapat
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.user-login-button-container').length) {
                    $('.user-login-menu-container').removeClass('active');
                }
            });
            
            // Mobil cihazlarda tıklama ile menüyü aç/kapat
            $('.user-account-button').on('click', function(e) {
                if (window.innerWidth <= 768) {
                    e.preventDefault();
                    $(this).next('.user-login-menu-container').toggleClass('active');
                }
            });
        });
    </script>
    <style>
        /* Mobil cihazlarda aktif menü stili */
        @media (max-width: 768px) {
            .user-login-menu-container {
                display: none;
            }
            .user-login-menu-container.active {
                display: block;
                opacity: 1;
                visibility: visible;
                transform: translateY(0);
            }
        }
    </style>
    <?php
}
add_action('wp_footer', 'olb_user_menu_scripts');


/** 
 * Add total auction bids count after price with transient caching
 */
function olb_add_total_auction_after_price() {
    global $product;
    
    if (!$product || !$product->is_type('auction')) {
        return;
    }
    
    // Transient anahtarı oluştur
    $transient_key = 'olb_auction_bids_count_' . $product->get_id();
    
    // Transient'i kontrol et
    $bids_count = get_transient($transient_key);
    
    // Eğer transient yoksa veya süresi dolmuşsa, veritabanından çek
    if (false === $bids_count) {
        global $wpdb;
        $bids_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}auctions_for_woocommerce_log WHERE auction_id = %d",
            $product->get_id()
        ));
        
        // Sonucu transient'e kaydet (1 saat geçerli)
        set_transient($transient_key, $bids_count, HOUR_IN_SECONDS);
    }
 
    
    echo '<div class="total-auctions"><a href="#" id="auctions-history-link">' . $bids_count. __(' Teklif', 'woocommerce') . '</a><p class="dot">•</p>';
    wc_get_template('global/auction-counter.php');
    echo '</div>';
    
    // Ürün durumu attribute'larını göster
    echo '<div class="product-status">';
    
    // Ürün attribute'larını al
    $attributes = $product->get_attributes();
    
    if (!empty($attributes)) {
        echo '<div class="all-attributes">';
        foreach ($attributes as $attribute_name => $attribute) {
            $attribute_label = wc_attribute_label($attribute_name);
            $attribute_value = $product->get_attribute($attribute_name);
            
            if (!empty($attribute_value)) {
                echo '<div class="attribute-item">';
                
                echo '<span class="attribute-value">' . esc_html($attribute_value) . '</span>';
                echo '<span class="attribute-label">' . esc_html($attribute_label) . ':</span> ';
                echo '<span class="product-status-description-link" id="product-status-info"><svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
		<path d="M7.99966 0.5C3.58197 0.5 0 4.08197 0 8.49966C0 12.9174 3.58197 16.5 7.99966 16.5C12.4174 16.5 16 12.9174 16 8.49966C16 4.08197 12.4174 0.5 7.99966 0.5ZM9.66502 12.8984C9.25325 13.0609 8.92546 13.1842 8.67962 13.2695C8.43445 13.3549 8.14933 13.3975 7.82493 13.3975C7.32648 13.3975 6.93841 13.2756 6.6621 13.0325C6.38578 12.7894 6.2483 12.4812 6.2483 12.1067C6.2483 11.9611 6.25845 11.8121 6.27877 11.6604C6.29977 11.5087 6.33295 11.338 6.37833 11.1463L6.89371 9.3259C6.93909 9.15117 6.97837 8.98525 7.00952 8.83084C7.04068 8.67507 7.05558 8.53217 7.05558 8.40214C7.05558 8.17052 7.00749 8.00798 6.912 7.91655C6.81515 7.82512 6.63297 7.78042 6.3614 7.78042C6.22866 7.78042 6.09185 7.80006 5.95166 7.84138C5.81283 7.88404 5.69228 7.92265 5.5934 7.96057L5.72952 7.39981C6.06679 7.26233 6.38984 7.14449 6.69799 7.04696C7.00614 6.94808 7.29735 6.89932 7.57164 6.89932C8.06671 6.89932 8.44868 7.01987 8.71754 7.25826C8.98506 7.49733 9.11983 7.80819 9.11983 8.19016C9.11983 8.2694 9.11035 8.40891 9.09206 8.60802C9.07378 8.80781 9.03924 8.98999 8.98912 9.15727L8.47644 10.9723C8.43445 11.1179 8.39721 11.2845 8.36334 11.4708C8.33016 11.657 8.3139 11.7992 8.3139 11.8947C8.3139 12.1358 8.36741 12.3004 8.47577 12.3877C8.58277 12.4751 8.77037 12.5191 9.03585 12.5191C9.16114 12.5191 9.30133 12.4968 9.45981 12.4534C9.61693 12.4101 9.73071 12.3715 9.8025 12.3383L9.66502 12.8984ZM9.57426 5.53128C9.3352 5.75342 9.04737 5.86449 8.71077 5.86449C8.37486 5.86449 8.08499 5.75342 7.84389 5.53128C7.60415 5.30914 7.48292 5.03892 7.48292 4.72332C7.48292 4.4084 7.60483 4.1375 7.84389 3.91333C8.08499 3.68849 8.37486 3.57674 8.71077 3.57674C9.04737 3.57674 9.33587 3.68849 9.57426 3.91333C9.81333 4.1375 9.93321 4.4084 9.93321 4.72332C9.93321 5.0396 9.81333 5.30914 9.57426 5.53128Z" fill="#050632"></path>
		</svg></span>';
                echo '</div>';
            }
        }
        echo '</div>';
    }
    
    echo '</div>';
}
add_action('woocommerce_single_product_summary', 'olb_add_total_auction_after_price', 20);

/**
 * Clear auction bids count transient when a new bid is placed or auction status changes
 */
function olb_clear_auction_bids_count_transient($product_id) {
    delete_transient('olb_auction_bids_count_' . $product_id);
}

// Teklif verildiğinde transient'i temizle
add_action('auctions_for_woocommerce_before_place_bid', 'olb_clear_auction_bids_count_transient', 10, 1);
add_action('auctions_for_woocommerce_outbid', 'olb_clear_auction_bids_count_transient', 10, 1);
add_action('auctions_for_woocommerce_place_bid', 'olb_clear_auction_bids_count_transient', 10, 1);

// Açık artırma durumu değiştiğinde transient'i temizle
add_action('auctions_for_woocommerce_started', 'olb_clear_auction_bids_count_transient', 10, 1);
add_action('auctions_for_woocommerce_finished', 'olb_clear_auction_bids_count_transient', 10, 1);
add_action('auctions_for_woocommerce_close', 'olb_clear_auction_bids_count_transient', 10, 1);
add_action('auctions_for_woocommerce_fail', 'olb_clear_auction_bids_count_transient', 10, 1);
add_action('auctions_for_woocommerce_reserve_fail', 'olb_clear_auction_bids_count_transient', 10, 1);
add_action('auctions_for_woocommerce_won', 'olb_clear_auction_bids_count_transient', 10, 1);
add_action('auctions_for_woocommerce_delete_bid', 'olb_clear_auction_bids_count_transient', 10, 1);
 

 
/**
 * Remove auctions history tab from WooCommerce single product tabs
 */
function olb_remove_auction_history_tab($tabs) {
    $tabs['description']['title'] = __('Ürün Özellikleri', 'woocommerce');
    // Remove the auctions_for_woocommerce_history tab
    if (isset($tabs['auctions_for_woocommerce_history'])) {
        unset($tabs['auctions_for_woocommerce_history']);
    }
    return $tabs;
}
add_filter('woocommerce_product_tabs', 'olb_remove_auction_history_tab', 99);
 
/**
 * Add auction history popup to single product page
 */
function olb_add_auction_history_popup() {
    global $product;
    
    // Only for auction products
    if (!$product || !$product->is_type('auction')) {
        return;
    }
    
    echo '<div id="auction-history-popup" class="auction-history-popup">';
    echo '<div class="auction-history-popup-content">';
    echo '<span class="close-popup">&times;</span>';
    echo '<h3>' . __('Teklif Geçmişi', 'woocommerce') . '</h3>';
    echo '<div class="auction-history-content">';
    wc_get_template('single-product/tabs/auction-history.php');
    echo '</div>';
    echo '</div>';
    echo '</div>';
}
add_action('woocommerce_after_single_product', 'olb_add_auction_history_popup');
 

function olb_buy_now_popup() {
    global $product;
    
    if (!$product || !$product->is_type('auction')) {
        return;
    }

    echo '<div id="buy-now-popup" class="buy-now-popup">';
    echo '<div class="buy-now-popup-content">
    <div class="buy-now-popup-inner">
    ';
    echo '<span class="close-popup">&times;</span>';
    echo '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 48 48" fill="none">
        <path d="M23.999 0C10.7459 0 0 10.7459 0 23.999C0 37.2521 10.7459 48 23.999 48C37.2521 48 48 37.2521 48 23.999C48 10.7459 37.2521 0 23.999 0ZM28.995 37.1952C27.7597 37.6828 26.7764 38.0526 26.0389 38.3086C25.3034 38.5646 24.448 38.6926 23.4748 38.6926C21.9794 38.6926 20.8152 38.3269 19.9863 37.5975C19.1573 36.8681 18.7449 35.9436 18.7449 34.8201C18.7449 34.3832 18.7754 33.9363 18.8363 33.4811C18.8993 33.026 18.9989 32.514 19.135 31.9391L20.6811 26.4777C20.8173 25.9535 20.9351 25.4557 21.0286 24.9925C21.122 24.5252 21.1667 24.0965 21.1667 23.7064C21.1667 23.0116 21.0225 22.5239 20.736 22.2497C20.4455 21.9754 19.8989 21.8413 19.0842 21.8413C18.686 21.8413 18.2756 21.9002 17.855 22.0241C17.4385 22.1521 17.0768 22.2679 16.7802 22.3817L17.1886 20.6994C18.2004 20.287 19.1695 19.9335 20.094 19.6409C21.0184 19.3443 21.8921 19.198 22.7149 19.198C24.2001 19.198 25.346 19.5596 26.1526 20.2748C26.9552 20.992 27.3595 21.9246 27.3595 23.0705C27.3595 23.3082 27.331 23.7267 27.2762 24.3241C27.2213 24.9234 27.1177 25.47 26.9674 25.9718L25.4293 31.4169C25.3034 31.8537 25.1916 32.3535 25.09 32.9123C24.9905 33.471 24.9417 33.8977 24.9417 34.1841C24.9417 34.9074 25.1022 35.4011 25.4273 35.6632C25.7483 35.9253 26.3111 36.0574 27.1076 36.0574C27.4834 36.0574 27.904 35.9903 28.3794 35.8603C28.8508 35.7303 29.1921 35.6145 29.4075 35.5149L28.995 37.1952ZM28.7228 15.0938C28.0056 15.7603 27.1421 16.0935 26.1323 16.0935C25.1246 16.0935 24.255 15.7603 23.5317 15.0938C22.8124 14.4274 22.4488 13.6168 22.4488 12.67C22.4488 11.7252 22.8145 10.9125 23.5317 10.24C24.255 9.56546 25.1246 9.23022 26.1323 9.23022C27.1421 9.23022 28.0076 9.56546 28.7228 10.24C29.44 10.9125 29.7996 11.7252 29.7996 12.67C29.7996 13.6188 29.44 14.4274 28.7228 15.0938Z" fill="#050632"/>
        </svg>';
    echo '<p class="buy-now-popup-title">Bu ürünü açık artırmayı beklemeden '.wc_price( $product->get_regular_price() ).'’ya hemen satın alabilirsiniz.</p>';
    echo '</div> </div> </div>';
}
add_action('woocommerce_after_single_product', 'olb_buy_now_popup');



function olb_product_status_popup() {
    global $product;
    
    if (!$product || !$product->is_type('auction')) {
        return;
    }

    echo '<div id="product-status-popup" class="product-status-popup">';
    echo '<div class="product-status-popup-content">
            <div class="product-status-popup-inner">
    ';
    echo '<span class="close-popup">&times;</span>';
     
    echo '<p class="product-status-popup-title">Ürün Durumu</p>';
    echo '</div> </div> </div>';
}
add_action('woocommerce_after_single_product', 'olb_product_status_popup');
 
 // enable gutenberg for woocommerce
function activate_gutenberg_product( $can_edit, $post_type ) {
    if ( $post_type == 'product' ) {
    $can_edit = true;
    }
    return $can_edit;
    }
    add_filter( 'use_block_editor_for_post_type', 'activate_gutenberg_product', 10, 2 );
    // enable taxonomy fields for woocommerce with gutenberg on
    function enable_taxonomy_rest( $args ) {
    $args['show_in_rest'] = true;
    return $args;
    }
    add_filter( 'woocommerce_taxonomy_args_product_cat', 'enable_taxonomy_rest' );
    add_filter( 'woocommerce_taxonomy_args_product_tag', 'enable_taxonomy_rest' );

/**
 * Disable WooCommerce reviews
 */
function olb_disable_woocommerce_reviews() {
    // Remove reviews tab from product tabs
    add_filter('woocommerce_product_tabs', 'olb_remove_reviews_tab', 98);
    
    // Disable support for reviews in WooCommerce
    remove_post_type_support('product', 'comments');
    remove_post_type_support('product', 'reviews');
    
    // Close comments on products
    add_filter('comments_open', 'olb_close_product_comments', 10, 2);
}
add_action('init', 'olb_disable_woocommerce_reviews');

/**
 * Remove reviews tab from product tabs
 */
function olb_remove_reviews_tab($tabs) {
    unset($tabs['reviews']);
    return $tabs;
}

/**
 * Close comments on products
 */
function olb_close_product_comments($open, $post_id) {
    if (get_post_type($post_id) === 'product') {
        $open = false;
    }
    return $open;
}

/**
 * Add "İptal ve İade Koşulları" tab to WooCommerce product tabs
 */
function olb_add_refund_policy_tab($tabs) {
    // Add the new tab
    $tabs['refund_policy'] = array(
        'title'    => __('İptal ve İade Koşulları', 'woocommerce'),
        'priority' => 30,
        'callback' => 'olb_refund_policy_tab_content'
    );
    
    return $tabs;
}
add_filter('woocommerce_product_tabs', 'olb_add_refund_policy_tab', 95);

/**
 * Content for the "İptal ve İade Koşulları" tab
 */
function olb_refund_policy_tab_content() {
    // You can either use a static content here
  
     wc_get_template('single-product/tabs/refund-policy.php');
}

/**
 * Include OTP Login System
 */
function olb_include_otp_login() {
    require_once get_stylesheet_directory() . '/otp_login/otp-login.php';
}
add_action('after_setup_theme', 'olb_include_otp_login');

/**
 * Remove Downloads endpoint from My Account navigation
 */
function olb_remove_downloads_from_account_menu($items) {
    unset($items['downloads']);
    return $items;
}
add_filter('woocommerce_account_menu_items', 'olb_remove_downloads_from_account_menu');

/**
 * Add custom product gallery slider
 */
function olb_custom_product_gallery() {
    if (is_product()) {
        // Enqueue Slick Slider
        wp_enqueue_style('slick-slider', 'https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css', array(), '1.8.1');
        wp_enqueue_style('slick-slider-theme', 'https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.css', array(), '1.8.1');
        wp_enqueue_script('slick-slider', 'https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js', array('jquery'), '1.8.1', true);
        
        // Enqueue Fancybox for lightbox - using version 4 instead of 5 for better compatibility
        wp_enqueue_style('fancybox', 'https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.css', array(), '4.0');
        wp_enqueue_script('fancybox', 'https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.umd.js', array('jquery'), '4.0', true);
        
        // Enqueue our custom gallery script and styles
        wp_enqueue_style('olb-product-gallery', get_stylesheet_directory_uri() . '/assets/css/product-gallery.css', array(), filemtime(get_stylesheet_directory() . '/assets/css/product-gallery.css'));
        wp_enqueue_script('olb-product-gallery', get_stylesheet_directory_uri() . '/assets/js/product-gallery.js', array('jquery', 'slick-slider', 'fancybox'), filemtime(get_stylesheet_directory() . '/assets/js/product-gallery.js'), true);
    }
}
add_action('wp_enqueue_scripts', 'olb_custom_product_gallery');

/**
 * Remove default WooCommerce gallery scripts to avoid conflicts
 */
function olb_remove_zoom_lightbox_gallery() {
    if (is_product()) {
        wp_dequeue_style('woocommerce_prettyPhoto_css');
        wp_dequeue_script('prettyPhoto');
        wp_dequeue_script('prettyPhoto-init');
        
        // Remove zoom functionality
        remove_theme_support('wc-product-gallery-zoom');
        
        // Remove lightbox functionality
        remove_theme_support('wc-product-gallery-lightbox');
        
        // Remove slider functionality
        remove_theme_support('wc-product-gallery-slider');
    }
}
add_action('wp_enqueue_scripts', 'olb_remove_zoom_lightbox_gallery', 99);

/**
 * Remove WooCommerce sale flash badge from single product pages only
 */
function olb_remove_sale_flash($html, $post, $product) {
    if (is_product()) {
        return '';
    }
    return $html;
}
add_filter('woocommerce_sale_flash', 'olb_remove_sale_flash', 10, 3);


add_filter( 'wpgb_tax_query_args', function( $args, $facet ) {
    // Facet ID'si ile sınırlayabilirsin (gerekirse)
    if ( $facet['id'] === 1 ) { // 1 yerine kendi facet ID'ni yaz
        $args['hide_empty'] = false;
    }
    return $args;
}, 10, 2 );

/**
 * Add product loop enhancements
 */
function olb_product_loop_enhancements() {
    // Enqueue on shop/archive pages and any page that might contain our shortcodes
    if (is_shop() || is_product_category() || is_product_tag() || is_page() || is_front_page() || is_home()) {
        wp_enqueue_script('olb-product-loop', get_stylesheet_directory_uri() . '/assets/js/product-loop.js', array('jquery'), filemtime(get_stylesheet_directory() . '/assets/js/product-loop.js'), true);
    }
}
add_action('wp_enqueue_scripts', 'olb_product_loop_enhancements');
