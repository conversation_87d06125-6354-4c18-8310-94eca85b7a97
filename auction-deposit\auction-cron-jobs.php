<?php
/**
 * Auction Cron Jobs
 * 
 * Handles all cron job functionality for the auction penalty system:
 * - Check finished auctions and winners
 * - Monitor 24-hour purchase window
 * - Process penalty updates
 * 
 * @package AuctionPenaltySystem
 * @version 1.0.0
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Auction Cron Jobs Class
 */
class AuctionCronJobs {
    
    /**
     * Constructor
     */
    public function __construct() {
        // Hook into WordPress cron system
        add_action('auction_check_winners_cron', array($this, 'check_auction_winners'));
        add_action('auction_process_penalties_cron', array($this, 'process_penalty_updates'));
    }
    
    /**
     * Setup cron schedules
     */
    public function setup_cron_schedules() {
        // Add custom cron intervals
        add_filter('cron_schedules', array($this, 'add_cron_intervals'));
        
        // Schedule cron jobs if not already scheduled
        if (!wp_next_scheduled('auction_check_winners_cron')) {
            wp_schedule_event(time(), 'every_minute', 'auction_check_winners_cron');
        }
        
        if (!wp_next_scheduled('auction_process_penalties_cron')) {
            wp_schedule_event(time(), 'hourly', 'auction_process_penalties_cron');
        }
    }
    
    /**
     * Add custom cron intervals
     */
    public function add_cron_intervals($schedules) {
        $schedules['every_minute'] = array(
            'interval' => 60,
            'display' => __('Every Minute', 'woocommerce')
        );
        
        return $schedules;
    }
    
    /**
     * Check auction winners and track purchase window
     */
    public function check_auction_winners() {
        global $wpdb;
        
        // Get all finished auctions that haven't been processed
        $finished_auctions = $wpdb->get_results($wpdb->prepare("
            SELECT p.ID, p.post_title, pm1.meta_value as winner_id, pm2.meta_value as end_time, pm3.meta_value as closed_status
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm1 ON p.ID = pm1.post_id AND pm1.meta_key = '_auction_current_bider'
            LEFT JOIN {$wpdb->postmeta} pm2 ON p.ID = pm2.post_id AND pm2.meta_key = '_auction_dates_to'
            LEFT JOIN {$wpdb->postmeta} pm3 ON p.ID = pm3.post_id AND pm3.meta_key = '_auction_closed'
            LEFT JOIN {$wpdb->postmeta} pm4 ON p.ID = pm4.post_id AND pm4.meta_key = '_auction_penalty_processed'
            WHERE p.post_type = 'product'
            AND pm1.meta_value IS NOT NULL
            AND pm1.meta_value != ''
            AND pm3.meta_value = '2'
            AND pm4.meta_value IS NULL
            AND pm2.meta_value < %s
        ", current_time('mysql')));
        
        foreach ($finished_auctions as $auction) {
            $this->process_finished_auction($auction);
        }
        
        // Check for expired purchase windows
        $this->check_expired_purchase_windows();
    }
    
    /**
     * Process a finished auction
     */
    private function process_finished_auction($auction) {
        $product_id = $auction->ID;
        $winner_id = $auction->winner_id;
        
        if (!$winner_id) {
            return;
        }
        
        // Create winner tracking record
        $this->create_winner_tracking_record($product_id, $winner_id);
        
        // Mark auction as penalty processed
        update_post_meta($product_id, '_auction_penalty_processed', '1');
        
        // Log the action
        error_log("Auction Penalty System: Tracking winner for auction {$product_id}, winner: {$winner_id}");
    }
    
    /**
     * Create winner tracking record
     */
    private function create_winner_tracking_record($product_id, $winner_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'auction_winner_tracking';
        
        // Create table if it doesn't exist
        $this->create_winner_tracking_table();
        
        // Check if record already exists
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE product_id = %d AND winner_id = %d",
            $product_id, $winner_id
        ));
        
        if (!$existing) {
            $wpdb->insert(
                $table_name,
                array(
                    'product_id' => $product_id,
                    'winner_id' => $winner_id,
                    'auction_end_time' => current_time('mysql'),
                    'purchase_deadline' => date('Y-m-d H:i:s', strtotime('+24 hours')),
                    'status' => 'pending'
                ),
                array('%d', '%d', '%s', '%s', '%s')
            );
        }
    }
    
    /**
     * Check for expired purchase windows
     */
    private function check_expired_purchase_windows() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'auction_winner_tracking';
        
        // Get expired purchase windows
        $expired_winners = $wpdb->get_results($wpdb->prepare("
            SELECT * FROM $table_name 
            WHERE status = 'pending' 
            AND purchase_deadline < %s
        ", current_time('mysql')));
        
        foreach ($expired_winners as $winner) {
            $this->process_expired_winner($winner);
        }
    }
    
    /**
     * Process expired winner (didn't purchase in 24 hours)
     */
    private function process_expired_winner($winner) {
        global $wpdb;
        
        $product_id = $winner->product_id;
        $winner_id = $winner->winner_id;
        
        // Check if user actually purchased the item
        if ($this->has_user_purchased_auction($winner_id, $product_id)) {
            // User purchased, mark as completed
            $wpdb->update(
                $wpdb->prefix . 'auction_winner_tracking',
                array('status' => 'purchased'),
                array('id' => $winner->id),
                array('%s'),
                array('%d')
            );
            return;
        }
        
        // User didn't purchase, apply penalty
        $this->apply_penalty_to_user($winner_id, $product_id);
        
        // Remove winner and set second bidder as winner
        $this->reassign_auction_winner($product_id);
        
        // Update tracking record
        $wpdb->update(
            $wpdb->prefix . 'auction_winner_tracking',
            array('status' => 'penalty_applied'),
            array('id' => $winner->id),
            array('%s'),
            array('%d')
        );
        
        error_log("Auction Penalty System: Applied penalty to user {$winner_id} for auction {$product_id}");
    }
    
    /**
     * Check if user purchased the auction item
     */
    private function has_user_purchased_auction($user_id, $product_id) {
        global $wpdb;
        
        // Check if there's an order for this product by this user
        $order_exists = $wpdb->get_var($wpdb->prepare("
            SELECT o.ID 
            FROM {$wpdb->posts} o
            JOIN {$wpdb->postmeta} om ON o.ID = om.post_id
            JOIN {$wpdb->prefix}woocommerce_order_items oi ON o.ID = oi.order_id
            JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim ON oi.order_item_id = oim.order_item_id
            WHERE o.post_type = 'shop_order'
            AND om.meta_key = '_customer_user'
            AND om.meta_value = %d
            AND oim.meta_key = '_product_id'
            AND oim.meta_value = %d
            AND o.post_status IN ('wc-processing', 'wc-completed', 'wc-on-hold')
        ", $user_id, $product_id));
        
        return !empty($order_exists);
    }
    
    /**
     * Apply penalty to user
     */
    private function apply_penalty_to_user($user_id, $product_id) {
        // Increase user penalty level
        AuctionPenaltySystem::increase_user_penalty($user_id);
        
        // Log the penalty
        $this->log_penalty_application($user_id, $product_id);
    }
    
    /**
     * Reassign auction winner to second highest bidder
     */
    private function reassign_auction_winner($product_id) {
        global $wpdb;
        
        // Get second highest bidder
        $second_bidder = $wpdb->get_row($wpdb->prepare("
            SELECT userid, bid 
            FROM {$wpdb->prefix}auctions_for_woocommerce_log 
            WHERE auction_id = %d 
            ORDER BY bid DESC, date ASC 
            LIMIT 1, 1
        ", $product_id));
        
        if ($second_bidder) {
            // Update auction winner
            update_post_meta($product_id, '_auction_current_bider', $second_bidder->userid);
            update_post_meta($product_id, '_auction_current_bid', $second_bidder->bid);
            
            // Create new winner tracking record
            $this->create_winner_tracking_record($product_id, $second_bidder->userid);
            
            error_log("Auction Penalty System: Reassigned auction {$product_id} to user {$second_bidder->userid}");
        }
    }
    
    /**
     * Log penalty application
     */
    private function log_penalty_application($user_id, $product_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'auction_penalty_log';
        
        $wpdb->insert(
            $table_name,
            array(
                'user_id' => $user_id,
                'penalty_level' => AuctionPenaltySystem::get_user_penalty_level($user_id),
                'reason' => "Failed to purchase auction product {$product_id} within 24 hours",
                'date_created' => current_time('mysql')
            ),
            array('%d', '%d', '%s', '%s')
        );
    }
    
    /**
     * Process penalty updates (placeholder for future enhancements)
     */
    public function process_penalty_updates() {
        // This can be used for periodic penalty reviews, reductions, etc.
        // For now, it's a placeholder
        error_log("Auction Penalty System: Processing penalty updates");
    }
    
    /**
     * Create winner tracking table
     */
    private function create_winner_tracking_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'auction_winner_tracking';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            product_id bigint(20) NOT NULL,
            winner_id bigint(20) NOT NULL,
            auction_end_time datetime NOT NULL,
            purchase_deadline datetime NOT NULL,
            status varchar(20) DEFAULT 'pending',
            date_created datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY product_id (product_id),
            KEY winner_id (winner_id),
            KEY status (status)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
}
