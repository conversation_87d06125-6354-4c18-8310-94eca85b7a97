body.single-product .inside-article {
  padding: 0 30px;
}
body.single-product .woocommerce-info , body.single-product .woocommerce-error , body.single-product .woocommerce-message {
    background-color: #333;
    color: #fff;
    border-color: #000;
}


body.single-product .woocommerce-breadcrumb {
  padding: 0;
}

body.single-product.woocommerce div.product .product_title {
  font-family: "Manrope";
  font-style: normal;
  font-weight: 700;
  font-size: 36px;
  line-height: 44px;
  color: #000000;
  margin-bottom: 16px;
}

body.single-product.woocommerce ul.products li.product .price,
body.single-product.woocommerce div.product p.price {
  font-family: "Manrope";
  font-style: normal;
  font-weight: 500;
  font-size: 28px;
  line-height: 36px;
  color: #000000;
  margin-bottom: 32px;
}

body.single-product .total-auctions { 
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0px;
    gap: 4px;
    padding-bottom: 24px;
    border-bottom: 1px solid #E4E4E7;
    margin-bottom: 8px;
    position: relative;
}

body.single-product .total-auctions .dot { 
    color: #000;
    margin-left: 4px;
    text-decoration: none;
}

body.single-product .total-auctions p, body.single-product .total-auctions a {
    font-family: 'Manrope';
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px; 
    text-decoration-line: underline; 
    color: #A0A0A0;  
    margin-bottom: 0;
}

body.single-product .total-auctions .time-left {
    display: inline-block !important; 
    font-family: 'Manrope';
    font-style: normal;
    font-weight: 700;
    font-size: 12px !important;
    line-height: 16px !important; 
    color: #FB3848; 
    width: auto !important;
}

body.single-product .auction_form {
    display: flex;
    flex-direction: row; 
    padding: 0px;
    gap: 4px;
    padding-bottom: 24px;
    margin-bottom: 0 !important;
}

body.single-product .auction_form .quantity  {
    display: flex;
}

body.single-product .auction_form .quantity input.minus {
    background: #000;
    color: #fff; 
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px 12px 24px; 
    background: #050632; 
    border-radius: 100px 0px 0px 100px;  
}
body.single-product .auction_form .quantity input.plus {
    background: #000;
    color: #fff; 
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px 12px 24px; 
    background: #050632; 
    border-radius: 0px 100px 100px 0px;  
}
body.single-product .auction_form .quantity input.bid {
    max-width: unset !important;
    padding: 0 !important;
    border: 1px solid #A9A9A9;
}
body.single-product .auction_form .bid_button  { 
display: flex;
flex-direction: row;
align-items: center;
justify-content: center;
padding: 12px 48px;
gap: 16px; 
background: #050632 !important;
border-radius: 32px;  
font-family: 'Manrope';
font-style: normal;
font-weight: 500;
font-size: 14px;
line-height: 20px; 
color: #FFFFFF; 
height: auto;
max-width: 350px;
}

/** buy now button */
body.single-product .buy-now-content {
 
}
body.single-product .buy-now-content form {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    gap: 4px;
    margin-left: 8px;
    margin-bottom: 0 !important;
    margin-top: 12px !important;
}
body.single-product .buy-now-content .buy-now-button {
    background: transparent !important;
    color: #050632 !important; 
    padding: 0 !important;
    border: none !important;
    font-family: 'Manrope';
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    text-decoration: underline;
    margin-bottom: 0 !important;
    white-space: nowrap;
}
body.single-product .buy-now-content .buy-now-price {
 width: auto !important;
 white-space: nowrap;
    font-family: 'Manrope';
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px; ;
    margin-bottom: 0 !important;
}
body.single-product .buy-now-content #buy-now-info {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 4px;
    cursor: pointer;
}

body.single-product .wsawl-link {
    width: 100%;
    text-align: center;
}
body.single-product .add-wsawl.sa-watchlist-action:before, body.single-product .remove-wsawl.sa-watchlist-action:before {
    display: none !important;
}
body.single-product .add-wsawl.sa-watchlist-action,body.single-product .remove-wsawl.sa-watchlist-action {
    border-radius: 32px;
    border: 1px solid #050632;
    color: #050632;
    padding: 11px 48px; 
    font-family: 'Manrope';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 20px; 
    display: flex;
    align-items: center;
    text-align: center;  
    justify-content: center;
    gap: 8px;
    text-decoration: none;
    max-width: 508px;
    
}

body.single-product .remove-wsawl.sa-watchlist-action {
    background: #050632 !important;
    color: #fff !important;
}
body.single-product .remove-wsawl.sa-watchlist-action svg path {
    fill: #fff !important;
    stroke: #fff !important;
}

body.single-product a.button-login-to-add-watchlist {
    border-radius: 32px;
    border: 1px solid #050632;
    color: #050632;
    background: transparent !important;
    padding: 11px 48px; 
    font-family: 'Manrope';
    font-style: normal;
}
body.single-product a.button-login-to-add-watchlist:hover {
    background: #050632 !important;
    color: #fff !important;
} 
.woocommerce div.product form.cart::after, .woocommerce div.product form.cart::before {
    display: none !important;
}
body.single-product .watchlist-count {
    color: var(--Theme-Neutral-950, #121217);
    text-align: center; 
    font-family: Manrope;
    font-size: 12px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px; 
}

/** gallery */
.woocommerce #content div.product div.images, .woocommerce div.product div.images, .woocommerce-page #content div.product div.images, .woocommerce-page div.product div.images {
    width: 35% !important;
}

/** tabs */
body.single-product .woocommerce-tabs {
    border-radius: 4px;
    background: #FFF; 
    box-shadow: 0px 32px 64px -12px rgba(10, 13, 18, 0.20);
    padding: 40px;
    margin-bottom: 40px;
    width: 100%;
}

body.single-product div.product .woocommerce-tabs ul.tabs {
    border-radius: 8px;
    border: 1px solid var(--border-colors-foundations-border-default, #E1E1E1); 
    box-shadow: 3px 3px 10px 0px rgba(120, 132, 149, 0.08);
    width: fit-content;
}
body.single-product div.product .woocommerce-tabs ul.tabs li {
    border: none;
    border-right: 1px solid var(--border-colors-foundations-border-inset, #D8DEE4);
    background: var(--backgrounds-foundations-bg-default-white, #FFF); 
    box-shadow: 3px 3px 10px 0px rgba(120, 132, 149, 0.08);
    padding: 10px 40px;
    
}
body.single-product div.product .woocommerce-tabs ul.tabs li.active {
    border: none;
    border-right: 1px solid var(--border-colors-foundations-border-inset, #D8DEE4);
    background: var(--backgrounds-foundations-bg-subtle, #F6F8FA);
    box-shadow: 3px 3px 10px 0px rgba(120, 132, 149, 0.08);
}
body.single-product div.product .woocommerce-tabs ul.tabs li a {
    color: var(--Theme-Neutral-950, #121217); 
    font-family: 'Manrope';
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px; 
}
body.single-product div.product .woocommerce-tabs ul.tabs li a, body.single-product div.product .woocommerce-tabs ul.tabs li.active a {
    color: var(--Theme-Neutral-950, #121217); 
    font-family: 'Manrope';
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px; 
}

/* Auction History Popup Styles */
.auction-history-popup {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

.auction-history-popup-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 20px;
    border-radius: 8px;
    max-width: 600px;
    width: 90%;
    position: relative;
    font-family: 'Manrope', sans-serif;
}

.close-popup {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #666;
}

.close-popup:hover {
    color: #050632;
}

.auction-history-popup h3 {
    font-family: 'Manrope', sans-serif;
    font-weight: 700;
    font-size: 18px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #E4E4E7;
}

.auction-history-content {
    font-family: 'Manrope';
}

/** buy now popup */
.buy-now-popup {
    
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}
.buy-now-popup-content {
    width: 100%;
    height: 100%;
    position: relative;
    font-family: 'Manrope', sans-serif;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.buy-now-popup-content .buy-now-popup-inner {
    background-color: #fff;
    margin: 10% auto;
    padding: 24px;
    border-radius: 8px;
    max-width: 650px;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
}
.buy-now-popup-content .close-popup {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #666;
}
.buy-now-popup-content .buy-now-popup-title {
    font-family: 'Manrope';
    font-size: 18px;
    font-weight: 700;
    line-height: 20px;
    color: #050632;
    text-align: center;
}
.buy-now-popup-content svg {
    margin-bottom: 28px;
}

/** product status popup */
.product-status-popup {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
} 

.product-status-popup-content {
    width: 100%;
    height: 100%;
    position: relative;
    font-family: 'Manrope', sans-serif;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.product-status-popup-content .product-status-popup-inner {
    background-color: #fff;
    margin: 10% auto;
    padding: 24px;
    border-radius: 8px;
    max-width: 650px;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
}
.product-status-popup-content .close-popup {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #666;
}
.product-status-popup-content .product-status-popup-title {
    font-family: 'Manrope';
    font-size: 18px;
    font-weight: 700;
    line-height: 20px;
    color: #050632;
    text-align: center;
}
.product-status-popup-content svg {
    margin-bottom: 28px;
}


/* Make the link look clickable */
#auctions-history-link {
    cursor: pointer;
    text-decoration: underline;
    color: #A0A0A0;
}

#auctions-history-link:hover {
    color: #050632;
}
.auction-ajax-change {
    display: flex;
    flex-wrap: wrap;
}
.auction-ajax-change .max-bid {
    flex: 0 0 100%;
}

.bid-form-container {
    position: relative;
    max-width: 510px; 
    flex: 0 0 70%;
}

.bid-form-container .buy-now-content {
    position: absolute;
    top: 0;
    left: 100%; 
}



/* Ürün durumu stili */
.product-status {
    margin: 0 0 15px;
    padding: 0; 
}

.product-status .all-attributes {
    display: flex;
    flex-direction: row;
    gap: 8px;
}

.product-status .attribute-item {
    display: flex;
    align-items: center;
}

.product-status .attribute-label {
    font-weight: 600; 
    color: #666;
    font-family: 'Manrope', sans-serif;
    font-size: 14px;
}

.product-status .attribute-value {
    display: flex;
padding: var(--spacing-0-5, 2px) var(--spacing-2-5, 10px);
justify-content: center;
align-items: center;
gap: 10px;
border-radius: var(--border-radius-md, 6px);
border: 1px solid rgba(255, 255, 255, 0.00);
background: #24273F;
color: var(--Theme-Neutral-50, #F9F9FB);

/* Text XS/Bold */
font-family: Manrope;
font-size: 12px;
font-style: normal;
font-weight: 700;
line-height: 16px; /* 133.333% */ 
}

.product-status .product-status-description-link {
    height: 20px;
    cursor: pointer;
}
.product-status .product-status-description-link svg {
    pointer-events: none;
}

@media (max-width: 768px) {
    body.single-product .inside-article {
        padding: 0 15px;
    }
    .auction-ajax-change {
        flex-direction: column;
    }
    .bid-form-container {
        max-width: 100%;
    }
    body.single-product .buy-now-content form {
        justify-content: center;
    }
    body.single-product .buy-now-content #buy-now-info {
        flex: 0 0 18px;
    }
    body.single-product.woocommerce div.product .product_title {
        font-size: 24px;
        line-height: 32px;
    }

    body.single-product.woocommerce ul.products li.product .price, body.single-product.woocommerce div.product p.price {
        font-size: 20px;
        line-height: 28px;
    }
    body.single-product.woocommerce div.product .woocommerce-tabs {
        padding: 24px;
    }
    body.single-product.woocommerce div.product .woocommerce-tabs ul.tabs {
        padding: 0;
        width: 100%;
    }
    body.single-product.woocommerce div.product .woocommerce-tabs ul.tabs li a {
        font-size: 12px;
        line-height: 16px;
    }
    
}
