<?php
/**
 * Auction Admin Interface
 * 
 * Provides admin interface for managing auction penalties and monitoring the system
 * 
 * @package AuctionPenaltySystem
 * @version 1.0.0
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Auction Admin Interface Class
 */
class AuctionAdminInterface {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_update_user_penalty', array($this, 'ajax_update_user_penalty'));
        add_action('wp_ajax_reset_user_penalty', array($this, 'ajax_reset_user_penalty'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            __('Auction Penalty', 'woocommerce'),
            __('Auction Penalty', 'woocommerce'),
            'manage_woocommerce',
            'auction-penalty',
            array($this, 'admin_page')
        );
    }
    
    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'auction-penalty') === false) {
            return;
        }
        
        wp_enqueue_script('jquery');
        wp_enqueue_style('auction-penalty-admin', get_stylesheet_directory_uri() . '/auction-deposit/admin-styles.css', array(), '1.0.0');
    }
    
    /**
     * Main admin page
     */
    public function admin_page() {
        $tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'overview';
        
        ?>
        <div class="wrap">
            <h1><?php _e('Auction Penalty Management', 'woocommerce'); ?></h1>
            
            <nav class="nav-tab-wrapper">
                <a href="?page=auction-penalty&tab=overview" class="nav-tab <?php echo $tab == 'overview' ? 'nav-tab-active' : ''; ?>">
                    <?php _e('Overview', 'woocommerce'); ?>
                </a>
                <a href="?page=auction-penalty&tab=users" class="nav-tab <?php echo $tab == 'users' ? 'nav-tab-active' : ''; ?>">
                    <?php _e('User Penalties', 'woocommerce'); ?>
                </a>
                <a href="?page=auction-penalty&tab=tracking" class="nav-tab <?php echo $tab == 'tracking' ? 'nav-tab-active' : ''; ?>">
                    <?php _e('Winner Tracking', 'woocommerce'); ?>
                </a>
                <a href="?page=auction-penalty&tab=settings" class="nav-tab <?php echo $tab == 'settings' ? 'nav-tab-active' : ''; ?>">
                    <?php _e('Settings', 'woocommerce'); ?>
                </a>
            </nav>
            
            <div class="tab-content">
                <?php
                switch ($tab) {
                    case 'overview':
                        $this->display_overview_tab();
                        break;
                    case 'users':
                        $this->display_users_tab();
                        break;
                    case 'tracking':
                        $this->display_tracking_tab();
                        break;
                    case 'settings':
                        $this->display_settings_tab();
                        break;
                }
                ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * Display overview tab
     */
    private function display_overview_tab() {
        $deposit_requirements = new AuctionDepositRequirements();
        $winner_tracker = new AuctionWinnerTracker();
        
        $penalty_stats = $deposit_requirements->get_penalty_statistics();
        $tracking_stats = $winner_tracker->get_statistics();
        
        ?>
        <div class="auction-penalty-overview">
            <h2><?php _e('System Overview', 'woocommerce'); ?></h2>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <h3><?php _e('Penalty Statistics', 'woocommerce'); ?></h3>
                    <ul>
                        <li><?php printf(__('Level 0 (No penalty): %d users', 'woocommerce'), $penalty_stats['level_0'] ?? 0); ?></li>
                        <li><?php printf(__('Level 1 (10%% deposit): %d users', 'woocommerce'), $penalty_stats['level_1'] ?? 0); ?></li>
                        <li><?php printf(__('Level 2 (40%% deposit): %d users', 'woocommerce'), $penalty_stats['level_2'] ?? 0); ?></li>
                        <li><?php printf(__('Level 3 (Blocked): %d users', 'woocommerce'), $penalty_stats['level_3'] ?? 0); ?></li>
                    </ul>
                </div>
                
                <div class="stat-card">
                    <h3><?php _e('Winner Tracking', 'woocommerce'); ?></h3>
                    <ul>
                        <li><?php printf(__('Total tracked: %d', 'woocommerce'), $tracking_stats['total_tracked'] ?? 0); ?></li>
                        <li><?php printf(__('Purchased: %d', 'woocommerce'), $tracking_stats['purchased'] ?? 0); ?></li>
                        <li><?php printf(__('Penalties applied: %d', 'woocommerce'), $tracking_stats['penalties'] ?? 0); ?></li>
                        <li><?php printf(__('Success rate: %s%%', 'woocommerce'), $tracking_stats['success_rate'] ?? 0); ?></li>
                    </ul>
                </div>
            </div>
            
            <div class="cron-info">
                <h3><?php _e('Cron Job Information', 'woocommerce'); ?></h3>
                <p><?php _e('To ensure the penalty system works correctly, please set up the following cron jobs:', 'woocommerce'); ?></p>
                <code>* * * * * curl --silent <?php echo home_url('/?auction-penalty-cron=check-winners'); ?></code><br>
                <code>0 * * * * curl --silent <?php echo home_url('/?auction-penalty-cron=process-penalties'); ?></code>
                
                <p><strong><?php _e('Note:', 'woocommerce'); ?></strong> <?php _e('The first cron should run every minute, the second every hour.', 'woocommerce'); ?></p>
            </div>
        </div>
        <?php
    }
    
    /**
     * Display users tab
     */
    private function display_users_tab() {
        global $wpdb;
        
        // Get users with penalties
        $users_with_penalties = $wpdb->get_results("
            SELECT u.ID, u.display_name, u.user_email, um.meta_value as penalty_level
            FROM {$wpdb->users} u
            JOIN {$wpdb->usermeta} um ON u.ID = um.user_id
            WHERE um.meta_key = 'user_penalty'
            AND um.meta_value > 0
            ORDER BY um.meta_value DESC, u.display_name ASC
        ");
        
        ?>
        <div class="auction-penalty-users">
            <h2><?php _e('Users with Penalties', 'woocommerce'); ?></h2>
            
            <?php if (empty($users_with_penalties)): ?>
                <p><?php _e('No users currently have penalties.', 'woocommerce'); ?></p>
            <?php else: ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('User', 'woocommerce'); ?></th>
                            <th><?php _e('Email', 'woocommerce'); ?></th>
                            <th><?php _e('Penalty Level', 'woocommerce'); ?></th>
                            <th><?php _e('Description', 'woocommerce'); ?></th>
                            <th><?php _e('Actions', 'woocommerce'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users_with_penalties as $user): ?>
                            <tr>
                                <td><?php echo esc_html($user->display_name); ?></td>
                                <td><?php echo esc_html($user->user_email); ?></td>
                                <td>
                                    <span class="penalty-level penalty-level-<?php echo $user->penalty_level; ?>">
                                        <?php echo $user->penalty_level; ?>
                                    </span>
                                </td>
                                <td><?php echo AuctionPenaltySystem::get_penalty_description($user->penalty_level); ?></td>
                                <td>
                                    <button class="button button-small update-penalty" 
                                            data-user-id="<?php echo $user->ID; ?>" 
                                            data-current-level="<?php echo $user->penalty_level; ?>">
                                        <?php _e('Update', 'woocommerce'); ?>
                                    </button>
                                    <button class="button button-small reset-penalty" 
                                            data-user-id="<?php echo $user->ID; ?>">
                                        <?php _e('Reset', 'woocommerce'); ?>
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
        
        <!-- Update Penalty Modal -->
        <div id="update-penalty-modal" style="display: none;">
            <div class="modal-content">
                <h3><?php _e('Update Penalty Level', 'woocommerce'); ?></h3>
                <form id="update-penalty-form">
                    <input type="hidden" id="penalty-user-id" name="user_id" value="">
                    <label for="penalty-level"><?php _e('New Penalty Level:', 'woocommerce'); ?></label>
                    <select id="penalty-level" name="penalty_level">
                        <option value="0"><?php _e('0 - No penalty', 'woocommerce'); ?></option>
                        <option value="1"><?php _e('1 - 10% deposit required', 'woocommerce'); ?></option>
                        <option value="2"><?php _e('2 - 40% deposit required', 'woocommerce'); ?></option>
                        <option value="3"><?php _e('3 - Bidding blocked', 'woocommerce'); ?></option>
                    </select>
                    <div class="modal-actions">
                        <button type="submit" class="button button-primary"><?php _e('Update', 'woocommerce'); ?></button>
                        <button type="button" class="button cancel-modal"><?php _e('Cancel', 'woocommerce'); ?></button>
                    </div>
                </form>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            // Update penalty button
            $('.update-penalty').click(function() {
                var userId = $(this).data('user-id');
                var currentLevel = $(this).data('current-level');
                
                $('#penalty-user-id').val(userId);
                $('#penalty-level').val(currentLevel);
                $('#update-penalty-modal').show();
            });
            
            // Cancel modal
            $('.cancel-modal').click(function() {
                $('#update-penalty-modal').hide();
            });
            
            // Update penalty form
            $('#update-penalty-form').submit(function(e) {
                e.preventDefault();
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'update_user_penalty',
                        user_id: $('#penalty-user-id').val(),
                        penalty_level: $('#penalty-level').val(),
                        nonce: '<?php echo wp_create_nonce('update_penalty_nonce'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Error: ' + response.data);
                        }
                    }
                });
            });
            
            // Reset penalty button
            $('.reset-penalty').click(function() {
                if (confirm('<?php _e('Are you sure you want to reset this user\'s penalty?', 'woocommerce'); ?>')) {
                    var userId = $(this).data('user-id');
                    
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'reset_user_penalty',
                            user_id: userId,
                            nonce: '<?php echo wp_create_nonce('reset_penalty_nonce'); ?>'
                        },
                        success: function(response) {
                            if (response.success) {
                                location.reload();
                            } else {
                                alert('Error: ' + response.data);
                            }
                        }
                    });
                }
            });
        });
        </script>
        
        <style>
        .penalty-level {
            padding: 3px 8px;
            border-radius: 3px;
            color: white;
            font-weight: bold;
        }
        .penalty-level-1 { background-color: #ffc107; color: #000; }
        .penalty-level-2 { background-color: #dc3545; }
        .penalty-level-3 { background-color: #17a2b8; }
        
        #update-penalty-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 9999;
        }
        
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 5px;
            min-width: 300px;
        }
        
        .modal-actions {
            margin-top: 15px;
            text-align: right;
        }
        
        .modal-actions button {
            margin-left: 10px;
        }
        </style>
        <?php
    }
    
    /**
     * Display tracking tab
     */
    private function display_tracking_tab() {
        $winner_tracker = new AuctionWinnerTracker();
        $pending_winners = $winner_tracker->get_pending_winners();
        
        ?>
        <div class="auction-penalty-tracking">
            <h2><?php _e('Winner Tracking', 'woocommerce'); ?></h2>
            
            <?php if (empty($pending_winners)): ?>
                <p><?php _e('No pending auction winners.', 'woocommerce'); ?></p>
            <?php else: ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('Product', 'woocommerce'); ?></th>
                            <th><?php _e('Winner', 'woocommerce'); ?></th>
                            <th><?php _e('Auction End', 'woocommerce'); ?></th>
                            <th><?php _e('Purchase Deadline', 'woocommerce'); ?></th>
                            <th><?php _e('Status', 'woocommerce'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($pending_winners as $winner): ?>
                            <?php
                            $product = wc_get_product($winner->product_id);
                            $user = get_userdata($winner->winner_id);
                            $is_expired = strtotime($winner->purchase_deadline) < time();
                            ?>
                            <tr class="<?php echo $is_expired ? 'expired' : ''; ?>">
                                <td>
                                    <?php if ($product): ?>
                                        <a href="<?php echo get_edit_post_link($winner->product_id); ?>">
                                            <?php echo $product->get_name(); ?>
                                        </a>
                                    <?php else: ?>
                                        <?php _e('Product not found', 'woocommerce'); ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($user): ?>
                                        <?php echo $user->display_name; ?> (<?php echo $user->user_email; ?>)
                                    <?php else: ?>
                                        <?php _e('User not found', 'woocommerce'); ?>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('Y-m-d H:i:s', strtotime($winner->auction_end_time)); ?></td>
                                <td>
                                    <?php echo date('Y-m-d H:i:s', strtotime($winner->purchase_deadline)); ?>
                                    <?php if ($is_expired): ?>
                                        <span class="expired-label"><?php _e('EXPIRED', 'woocommerce'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo ucfirst($winner->status); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
        
        <style>
        .expired {
            background-color: #f8d7da !important;
        }
        .expired-label {
            color: #dc3545;
            font-weight: bold;
            font-size: 0.8em;
        }
        </style>
        <?php
    }
    
    /**
     * Display settings tab
     */
    private function display_settings_tab() {
        ?>
        <div class="auction-penalty-settings">
            <h2><?php _e('Settings', 'woocommerce'); ?></h2>
            
            <div class="settings-section">
                <h3><?php _e('Penalty Levels', 'woocommerce'); ?></h3>
                <table class="form-table">
                    <tr>
                        <th><?php _e('Level 0', 'woocommerce'); ?></th>
                        <td><?php _e('No penalty - Users can bid freely', 'woocommerce'); ?></td>
                    </tr>
                    <tr>
                        <th><?php _e('Level 1', 'woocommerce'); ?></th>
                        <td><?php _e('10% deposit required to bid', 'woocommerce'); ?></td>
                    </tr>
                    <tr>
                        <th><?php _e('Level 2', 'woocommerce'); ?></th>
                        <td><?php _e('40% deposit required to bid', 'woocommerce'); ?></td>
                    </tr>
                    <tr>
                        <th><?php _e('Level 3', 'woocommerce'); ?></th>
                        <td><?php _e('Bidding blocked completely', 'woocommerce'); ?></td>
                    </tr>
                </table>
            </div>
            
            <div class="settings-section">
                <h3><?php _e('System Information', 'woocommerce'); ?></h3>
                <p><?php _e('Purchase window: 24 hours after auction ends', 'woocommerce'); ?></p>
                <p><?php _e('Penalty increase: +1 level for each failed purchase', 'woocommerce'); ?></p>
                <p><?php _e('Winner reassignment: Automatic to second highest bidder', 'woocommerce'); ?></p>
            </div>
        </div>
        <?php
    }
    
    /**
     * AJAX handler for updating user penalty
     */
    public function ajax_update_user_penalty() {
        if (!wp_verify_nonce($_POST['nonce'], 'update_penalty_nonce')) {
            wp_die('Security check failed');
        }
        
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }
        
        $user_id = intval($_POST['user_id']);
        $penalty_level = intval($_POST['penalty_level']);
        
        if ($user_id && $penalty_level >= 0 && $penalty_level <= 3) {
            AuctionPenaltySystem::set_user_penalty_level($user_id, $penalty_level);
            wp_send_json_success('Penalty updated successfully');
        } else {
            wp_send_json_error('Invalid parameters');
        }
    }
    
    /**
     * AJAX handler for resetting user penalty
     */
    public function ajax_reset_user_penalty() {
        if (!wp_verify_nonce($_POST['nonce'], 'reset_penalty_nonce')) {
            wp_die('Security check failed');
        }
        
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }
        
        $user_id = intval($_POST['user_id']);
        
        if ($user_id) {
            AuctionPenaltySystem::set_user_penalty_level($user_id, 0);
            wp_send_json_success('Penalty reset successfully');
        } else {
            wp_send_json_error('Invalid user ID');
        }
    }
}
